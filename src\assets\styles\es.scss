.app-container-search {
  padding: 20px 30px 2px;
  box-sizing: border-box;
  background-color: #ffffff;
  margin-bottom: 15px;

  .el-form-item {
    width: 370px !important;
    margin-right: 25px !important;
  }

  .el-form-item__label {
    max-width: 130px !important;
    font-size: 14px !important;
    font-weight: 400 !important;
  }
  .el-form-item__content {
    width: 100% !important;
    .el-input,
    .el-select,
    .el-cascader,
    .el-autocomplete {
      width: 100% !important;
    }

    .el-date-editor.el-input {
      width: 100% !important;
    }
  }
  .w-auto {
    width: auto !important;
  }

  .one-column {
    width: 100% !important;
  }
}

.app-container-content {
  padding: 20px 30px;
  box-sizing: border-box;
  background-color: #ffffff;
}

.overview-wrap {
  &::before {
    content: '';
    display: inline-block;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background-color: $primary-color;
    margin-right: 10px;
  }
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333333;
  .num {
    color: $primary-color;
    font-weight: bold;
    margin-right: 20px;
  }
}
.container-bar {
  display: flex;
  justify-content: space-between;
  .bar-left {
    flex: 1;
  }
  .bar-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }
}

.dialog-form-inline {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;

  .el-form-item {
    width: 48%;
    display: flex;
    margin-right: 0px !important;

    .el-input,
    .el-select,
    .el-cascader,
    .el-textarea {
      width: 100% !important;
    }
  }

  .half-column {
    flex: 0 0 calc(47% + 10px) !important;
    margin-right: 0px !important;
  }

  .one-column {
    width: 100%;
  }
}

.app-select {
  font-size: 14px;
  width: 100%;
  height: 39.99px;
  line-height: 39.99px;
  padding: 1px 15px;
  display: flex;
  align-items: center;
  background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  transition: var(--el-transition-box-shadow);
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
  ._placeholder {
    color: var(--el-text-color-placeholder);
  }
}

.el-button--large {
  padding: 12px 22px !important;
}

.el-form-item.is-error .app-select {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}

.el-form-item {
  color: #333333 !important;
  // margin-right: 18px !important;
}

.el-form-item__label {
  font-size: 14px !important;
  color: #333333 !important;
  // height: 36px !important;
  // line-height: 36px !important;
  // margin-bottom: 0 !important;
}

.el-table .cell {
  font-size: 14px !important;
  color: #333333 !important;
  text-align: center !important;
  font-weight: 400 !important;
  // height: 22px !important;
  // line-height: 22px !important;
}

.el-table .cell.el-tooltip {
  font-size: 14px !important;
  color: #333333 !important;
}

.el-table thead .el-table__cell {
  background-color: #f6f7f9 !important;
}

.el-table .el-table__row--striped .el-table__cell {
  background-color: #f6f7f9 !important;
}

.el-table tbody tr:hover > td {
  background-color: #f6f7f9 !important;
}

// 抽屉
.maintain-wrap {
  .maintain-type {
    font-size: 14px;
    color: #8e9ab3;
  }
  .maintain-title-wrap {
    display: flex;
    overflow: hidden;
    .title-left {
      flex: 1;
      white-space: nowrap;
      overflow: hidden; //文本超出隐藏
      text-overflow: ellipsis; //文本超出省略号替代
      margin-right: auto;
      display: flex;
      align-items: center;
      .title {
        font-size: 22px;
        font-weight: bold;
        color: #333333;
      }
      .type {
        margin-left: 10px;
        width: 42px;
        height: 20px;
        text-align: center;
        border-radius: 3px;
        font-size: 14px;
        color: #fff;
      }
      .w58 {
        width: 58px !important;
      }
      .type-company {
        background: #00ba1f;
      }
      .type-person {
        background: #ffc000;
      }
      .type-0 {
        // 未归档
        background: #8f9bb3;
      }
      .type-1 {
        // 已归档
        background: #00ba1f;
      }
      .type-2 {
        // 已作废
        background: #ec0000;
      }
    }
    .title-right {
      margin-left: 30px;
    }
  }
  .maintain-btn-wrap {
    margin-top: 15px;
    span {
      margin-right: 8px;
      background: #e5e8ef;
      border-radius: 5px;
      font-size: 14px;
      color: #333333;
      padding: 5px 10px;
      cursor: pointer;
      &:hover {
        background: #dddddd;
      }
    }
  }
  .maintain-info-wrap {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
    background: #f6f7f9;
    border-radius: 5px;
    .maintain-info-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .t {
        font-size: 14px;
        color: #8f9bb3;
      }
      .d {
        margin-top: 5px;
        font-size: 14px;
        color: #333333;
      }
    }
  }
  .maintain-content-wrap {
    margin-top: 15px;
    display: flex;
    .maintain-tabs {
      flex: 1;
    }
    .maintain-summary-wrap {
      margin-left: 30px;
      width: 280px;
      .title {
        height: 44px;
        line-height: 44px;
        background: #2f5aff;
        border-radius: 5px 5px 0px 0px;
        font-size: 16px;
        font-weight: bold;
        color: #ffffff;
        padding-left: 30px;
        position: relative;
        &::before {
          content: '';
          width: 3px;
          height: 16px;
          background: #ffffff;
          border-radius: 2px;
          margin-left: 8px;
          position: absolute;
          top: 14px;
          left: 10px;
        }
      }
      ul,
      li {
        margin: 0;
        padding: 0;
        list-style: none;
      }
      .summary-list {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        min-height: 280px;
        border: 1px solid #e5e8ef;
        border-radius: 5px;
        .summary-item {
          display: flex;
          .t {
            margin-left: 30px;
            flex: 2;
          }
          .d {
            flex: 3;
          }
        }
      }
    }
  }
}

.workbench-tabs {
  :deep(.el-tabs__item) {
    font-size: 16px !important;
  }
  :deep(.el-tabs__item.is-active) {
    font-size: 16px !important;
    font-weight: bold;
  }
  :deep(.el-tabs__nav-wrap::after) {
    position: static !important;
  }
}
.app {
  &-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e4e8ee;

    &-item {
      color: #333333;
      font-size: 16px;
      position: relative;
      padding: 15px 20px;
      cursor: pointer;
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        color: $primary-color;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -1.5px;
        left: calc((100% - 30%) / 2);
        width: 30%;
        height: 3px;
        background-color: transparent;
        transition: all 0.3s ease;
        border-radius: 2px;
      }
    }

    &-active {
      font-weight: 600;
      color: $primary-color;

      &::after {
        background-color: $primary-color;
      }
    }
  }
}
.custom-tabs {
  border: 1px solid #f1f1f1;
  background: #ffffff;
  padding: 10px 0px;
  .el-tabs__header {
    background: #ffffff;
  }
  .el-tabs__nav-wrap {
    padding: 0 10px;
  }
  .el-tabs__active-bar {
    background-color: $primary-color;
    width: 0px !important;
  }

  .el-tabs__item {
    font-size: 16px;
    color: #333333;
    margin-bottom: 5px;
    &:hover {
      color: $primary-color;
    }
  }
  .el-tabs__item.is-top:nth-child(2) {
    padding-left: 20px !important;
  }
  .el-tabs__item.is-top:last-child {
    padding-right: 20px !important;
  }
  .el-tabs__item.is-active {
    color: $primary-color;
    font-weight: bold;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 43px;
      transform: translateX(-50%); /* 使伪元素水平居中 */
      width: calc(50% - 20px); /* 自定义长度的一半 */
      height: 3px; /* 自定义border-bottom的厚度 */
      background: $primary-color; /* 自定义border-bottom的颜色 */
      border-radius: 3px;
    }
  }
  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #e4e8ee;
  }
  .custom-tab-container {
    padding: 15px 30px 20px 30px;
  }
}
.stat-tabs {
  border: 1px solid #f1f1f1;
  background: #ffffff;
  padding: 5px 0px;
  .el-tabs__header {
    background: #ffffff;
    margin: 0px;
    padding: 0px;
  }
  .el-tabs__nav-wrap {
    padding: 0 10px;
  }
  .el-tabs__active-bar {
    background-color: $primary-color;
    width: 0px !important;
  }

  .el-tabs__item {
    font-size: 16px;
    color: #333333;
    margin-bottom: 10px;
    &:hover {
      color: $primary-color;
    }
  }
  .el-tabs__item.is-top:nth-child(2) {
    padding-left: 20px !important;
  }
  .el-tabs__item.is-top:last-child {
    padding-right: 20px !important;
  }
  .el-tabs__item.is-active {
    color: $primary-color;
    font-weight: bold;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 40px;
      transform: translateX(-50%); /* 使伪元素水平居中 */
      width: calc(50% - 20px); /* 自定义长度的一半 */
      height: 3px; /* 自定义border-bottom的厚度 */
      background: $primary-color; /* 自定义border-bottom的颜色 */
      border-radius: 3px;
    }
  }
  .el-tabs__nav-wrap::after {
    height: 0px;
  }
}

.stat-content{
  display: flex;
  flex-wrap: wrap;
  .stat-one-column {
    margin-top: 20px;
    flex: 0 0 100% !important;
    display: flex;
  }
  .stat-two-column {
    flex: 0 0 100% !important;
    display: flex;
    flex-wrap: wrap;
    gap: 0px 20px;
  }
  .half-column{
    margin-top: 20px;
    width: calc((100% - 20px)/2) !important;
  }
  .standard-chat{
    padding: 25px;
    background: #FFFFFF;
    box-sizing: border-box;
    .chat-title{
      font-weight: bold;
      font-size: 18px;
      color: #333333;
    }
    .chat-search{
      margin-top: 15px;
      .el-form-item--default{
        margin-bottom: 12px !important;
      }
      .el-form-item__label{
        font-size: 14px !important;
        color: #333333 !important;
      }
      .el-form-item__content {
        width: 350px !important;
        .el-select{
          width: 350px !important;
        }
        .btns{
          margin-left: 15px;
          display: flex;
          gap: 0px 10px;
          .el-button{
            margin: 0px !important;
          }
        }
      }
      .stat-date-picker .el-date-editor{
        width: 350px !important;
      }
      
    }
  }
}

.dialog-footer {
  padding-top: 10px !important;
  .el-button--primary {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
  }
  .el-button--plain {
    background-color: #ffffff !important;
    &:hover {
      color: $primary-color !important;
      border-color: $primary-color !important;
    }
  }
}

.el-descriptions__cell {
  border-color: #e3e3e3 !important;
}

.el-descriptions__label {
  width: 150px !important;
  min-width: 100px !important;
  font-size: 14px !important;
  color: #999999 !important;
  font-weight: 400 !important;
  margin-right: 0 !important;
  // background-color:  !important;
}

.el-descriptions__content {
  color: #333333 !important;
  // width: 325px !important;
  min-width: 260px !important;
}
.el-collapse {
  border: 1px solid #e8e8e8 !important;
}
.el-collapse-item__header {
  border-bottom-color: #e8e8e8 !important;
  padding: 0 20px !important;
  font-weight: bold !important;
  font-size: 18px !important;
  color: #333333 !important;
}
.el-collapse-item__content {
  padding: 20px !important;
}

.s-red {
  color: #ff0000 !important;
  background: #ffe4e4 !important;
  border-color: #ffe4e4 !important;
}
.s-blue {
  color: #2f5aff !important;
  background: #f2f5ff !important;
  border-color: #f2f5ff !important;
}
.s-green {
  color: #04ca29 !important;
  background: #dbfddd !important;
  border-color: #dbfddd !important;
}
.scroller-bar-style {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}
.app-dialog {
  .avatar {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0px;
    img {
      width: 100px;
      height: 100px;
      border-radius: 50%;
    }
  }
  .mobile-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .mobile-left {
      font-size: 16px;
      color: #999999;
      span {
        color: #333333;
      }
    }
    .mobile-right {
      .code-box {
        display: flex;
        align-items: center;
        height: 30px;
        background: $primary-color;
        border-radius: 3px;
        padding: 10px;
        box-sizing: border-box;
        font-size: 14px;
        color: #ffffff;
        cursor: pointer;
      }
      .down-timer {
        display: flex;
        align-items: center;
        height: 30px;
        background: #89b3ff;
        border-radius: 3px;
        padding: 10px;
        box-sizing: border-box;
        font-size: 14px;
        color: #ffffff;
      }
    }
  }
  .notice-title {
    font-weight: bold;
    font-size: 16px;
    color: #333333;
    position: relative;
    padding-left: 15px;
    &::before {
      content: '';
      position: absolute;
      left: 0px;
      top: 4px;
      width: 4px;
      height: 18px;
      background: $primary-color;
      border-radius: 2px;
    }
  }
  .el-input-group__append {
    padding: 0px;
    .verify {
      padding: 0 12px;
      text-align: center;
      background: $primary-color;
      font-size: 14px;
      color: #ffffff;
      cursor: pointer;
    }
    .down-timer {
      padding: 0 12px;
      text-align: center;
      background: #89b3ff;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
.el-button--primary {
  background-color: $primary-color !important;
  border-color: $primary-color !important;
}
.el-button--plain {
  background-color: #ffffff !important;
  &:hover {
    color: $primary-color !important;
    border-color: $primary-color !important;
  }
}
.el-button--primary.is-link {
  background-color: transparent !important;
  border-color: transparent !important;
}
.tip-box {
  background: #f8f9fb;
  border-radius: 3px;
  padding: 10px 20px;
  color: #888888;
  line-height: 20px;
  .mobile {
    color: #333333;
    font-weight: bold;
  }
}
