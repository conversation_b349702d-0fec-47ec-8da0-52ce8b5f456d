import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken, getUserInfo, setUserInfo, removeUserInfo } from '@/utils/auth'
import Aes from "@/utils/aes"

import Avatar from '@/assets/images/layout/avatar.png'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      userInfo: getUserInfo(),
      roles: [],
      permissions: [],
      showApplication: false,
    }),
    getters: {
      userId: (state) => state.userInfo.userId,
      userName: (state) => state.userInfo.userName,
      nickName: (state) => state.userInfo.nickName,
      avatar: (state) => state.userInfo.avatar ? state.userInfo.avatar : Avatar,
      phonenumber: (state) => Aes.decrypt(state.userInfo.phonenumber),
      email: (state) => state.userInfo.email,
    },
    actions: {
      // 登录
      login(userInfo) {
        const username = userInfo.username.trim()
        const password = userInfo.password
        const code = userInfo.code
        const uuid = userInfo.uuid
        return new Promise((resolve, reject) => {
          login(username, password, code, uuid).then(res => {
            setToken(res.data.accessToken)
            this.token = res.data.accessToken
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取用户信息
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            const user = res.data.user
            this.userInfo = user
            setUserInfo(user)
            resolve(res.data)
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 退出系统
      logOut() {
        return new Promise(async (resolve, reject) => {
          await logout(this.token).finally(() => {
            this.removeData()

            resolve()
          })
        })
      },
      removeData(){
        this.token = ''
        this.userInfo = {}
        this.roles = []
        this.permissions = []
        removeToken()
        removeUserInfo()
      }
    },
    setShowApplication(val){
      this.showApplication = val
    },
  })

export default useUserStore
