<template>
  <el-form :model="queryParams" :class="{'stat-form':type == 'oneLine'}" ref="queryFormRef" label-width="auto">
    <template v-if="type == 'oneLine'">
      <el-form-item label="标准发布日期">
      <div class="stat-date-picker">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDatePublish"
          :disabled-date="disabledDate"
        ></el-date-picker>
      </div>
    </el-form-item>
    <el-form-item v-if="queryParams.standardStatusList" label="标准状态" prop="standardStatusList">
      <el-select v-model="queryParams.standardStatusList" multiple placeholder="请选择标准状态" clearable>
        <el-option v-for="dict in bxc_standard_status" :key="dict.value" :label="dict.label" :value="dict.value" />
      </el-select>
    </el-form-item>
    <div class="btns w-auto">
      <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
      <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
    </div>
    </template>
    <template v-else>
      <el-form-item label="标准发布日期">
      <div class="stat-date-picker">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="selectDatePublish"
          :disabled-date="disabledDate"
        ></el-date-picker>
      </div>
      <div v-if="!queryParams.standardStatusList" class="btns">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
    </el-form-item>
    <el-form-item v-if="queryParams.standardStatusList" label="标准状态" prop="standardStatusList">
      <el-select v-model="queryParams.standardStatusList" multiple placeholder="请选择标准状态" clearable>
        <el-option v-for="dict in bxc_standard_status" :key="dict.value" :label="dict.label" :value="dict.value" />
      </el-select>
      <div class="btns">
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
    </el-form-item>
    <div v-if="!queryParams.standardStatusList" style="height: 44px;"></div>
    </template>
    
  </el-form>
</template>
<script setup>
const props = defineProps({
  type: {
    type: String,
    default: 'splitLine'
  },
  queryParams: {
    type: Object,
    default: () => {
      return {
        publishStartDate: undefined,
        publishEndDate: undefined,
      };
    }
  }
});
const { queryParams } = toRefs(props);
const { proxy } = getCurrentInstance();

const { bxc_standard_status } = proxy.useDict('bxc_standard_status');

const emit = defineEmits(['update:queryParams','change']);
const dateRange = ref([]);

const disabledDate = (time) => {
  return time.getTime() > Date.now();
}

const handleQuery = () => {
  emit('update:queryParams', queryParams.value);
  emit('change');
};
const selectDatePublish = () => {
  if (dateRange.value && dateRange.value.length > 0) {
    queryParams.value.publishStartDate = dateRange.value[0];
    queryParams.value.publishEndDate = dateRange.value[1];
  } else {
    queryParams.value.publishStartDate = undefined;
    queryParams.value.publishEndDate = undefined;
  }
}
const resetQuery = () => {
  proxy.$refs.queryFormRef.resetFields();
  dateRange.value = [];
  selectDatePublish();
  if (queryParams.value.standardStatusList) {
    queryParams.value.standardStatusList = ['1'];
  }

  emit('update:queryParams', queryParams.value);
  emit('change');
};

onMounted(() => {
  if (queryParams.value.standardStatusList) {
    queryParams.value.standardStatusList = ['1']
  }
});
</script>
<style lang="scss" scoped>
.stat-form{
  display: flex;
  .btns{
    margin-left: 15px;
  }
}
:deep(.el-tag.el-tag--info){
  color: $primary-color;
  background-color: #DBE8FF;
}
:deep(.el-tag .el-icon){
  color: #999999;
  :hover{
    color: #FFFFFF;
    background-color: $primary-color;
    border-radius: 50%;
  }
}
</style>