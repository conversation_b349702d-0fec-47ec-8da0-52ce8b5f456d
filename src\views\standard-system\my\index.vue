<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="formRef" label-width="auto" :inline="true" @submit.native.prevent>
        <el-form-item label="体系名称" prop="systemName">
          <el-input v-model="queryParams.systemName" placeholder="请输入体系名称" @keyup.enter="getData('pageNum')" />
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" icon="Search" @click="getData('pageNum')">查询</el-button>
          <el-button plain icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end">
        <el-button @click="handleClick('jump', 'manage?id=' + currentRow?.id)" :disabled="!currentRow">
          <span class="iconfont icon-tixiguanli f-14 mr5"></span>
          体系管理
        </el-button>
        <el-button type="primary" @click="handleClick('add')">
          <span class="iconfont icon-zengjia f-14 mr5"></span>
          添加体系
        </el-button>
        <el-button @click="handleClick('edit')" :disabled="!currentRow">
          <span class="iconfont icon-shezhi f-14 mr5"></span>
          体系设置
        </el-button>
        <el-button @click="handleClick('delete')" :disabled="!currentRow">
          <el-icon class="mr5"><Delete class="f-14" /></el-icon>
          删除
        </el-button>
      </div>
      <el-table
        ref="singleTableRef"
        v-loading="loading"
        :data="tableData"
        :border="false"
        highlight-current-row
        @current-change="handleCurrentChange"
        class="mt15"
      >
        <template v-slot:empty>
          <bxc-empty class="mt30" />
        </template>
        <el-table-column label="序号" fixed width="90">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="体系名称" min-width="200" fixed="left" show-overflow-tooltip>
          <template #default="{ row }">
            <span @click="handleClick('jump', 'detail?id=' + row.id)" class="c-primary pointer">
              {{ row.systemName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="标识" min-width="200">
          <template #default="{ row }">
            <img
              v-if="row.coverUrlFileList && row.coverUrlFileList.length > 0"
              :src="row.coverUrlFileList[0].url"
              alt=""
              style="height: 42px"
            />
          </template>
        </el-table-column>
        <el-table-column prop="nodeCount" label="节点数" min-width="120" show-overflow-tooltip />
        <el-table-column prop="standardCount" label="标准数" min-width="120" show-overflow-tooltip />
        <el-table-column prop="systemDescription" label="体系描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建日期" min-width="180" show-overflow-tooltip />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
    <add-dialog v-if="addVisible" v-model:visible="addVisible" v-model:id="id" @updateData="getData" />
  </div>
</template>
<script setup>
  import { getSystemList, deleteSystem } from '@/api/standard-system/my';
  import AddDialog from '@/views/components/standard-system/my/AddDialog.vue';

  const router = useRouter();
  const { proxy } = getCurrentInstance();

  const singleTableRef = ref(null);
  const addVisible = ref(false);
  const loading = ref(false);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const currentRow = ref();
  const id = ref('');

  const getData = data => {
    loading.value = true;
    if (data) queryParams.value.pageNum = 1;
    getSystemList(queryParams.value)
      .then(res => {
        tableData.value = res.rows || [];
        total.value = res.total || 0;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleReset = () => {
    proxy.$refs.formRef.resetFields();
    getData('pageNum');
  };

  const handleClick = (type, data) => {
    switch (type) {
      case 'jump':
        router.push(data);
        break;
      case 'add':
        addVisible.value = true;
        break;
      case 'edit':
        id.value = currentRow.value.id;
        addVisible.value = true;
        break;
      case 'delete':
        proxy
          .$confirm('确认删除【' + currentRow.value?.systemName + '】体系？确认删除后数据将不可恢复！', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            deleteSystem({ ids: [currentRow.value?.id] }).then(res => {
              singleTableRef.value.setCurrentRow();
              proxy.$modal.msgSuccess('删除成功！');
              getData();
            });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  const handleCurrentChange = val => {
    currentRow.value = val;
  };

  getData();
</script>

<style lang="scss" scoped>
  .app-container-content {
    padding: 20px 30px;
  }

  :deep(.el-button:focus) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:hover) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:active) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  // :deep(.el-button.is-disabled, .el-button.is-disabled:focus, .el-button.is-disabled:hover, .el-button.is-disabled:active) {
  //   color: var(--el-button-disabled-text-color) !important;
  //   cursor: not-allowed !important;
  //   background-image: none !important;
  //   background-color: var(--el-button-disabled-bg-color) !important;
  //   border-color: var(--el-button-disabled-border-color) !important;
  // }
</style>
