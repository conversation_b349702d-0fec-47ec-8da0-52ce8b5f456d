<template>
  <el-drawer title="账户设置" size="480" append-to-body v-model="props.open" :close-on-click-modal="false" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="app-dialog">
      <div class="avatar">
        <image-cropper
          v-model:coverImage="form.avatar"
        />
      </div>
      <el-form-item label="昵称" prop="nickName">
        <el-input
          size="large"
          v-model="form.nickName"
          placeholder="请输入昵称"

        />
      </el-form-item>
      <el-form-item label="真实姓名" prop="realName">
        <el-input
          size="large"
          v-model="form.realName"
          placeholder="请输入真实姓名"

        />
      </el-form-item>
      <el-form-item label="关注领域" prop="standardTypeCodeGbsList">
        <el-select
          style="width: 100%;"
          v-model="form.standardTypeCodeGbsList"
          multiple
          placeholder="选择所关注或行事行业领域"
          :multiple-limit="5"
        >
          <el-option
            v-for="item in ccsList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="mt50">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          style="width:100%;"
          @click.prevent="handleSave"
        >
          <span v-if="!loading">保 存</span>
          <span v-else>保 存 中...</span>
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="loading"
          size="large"
          style="width:100%;"
          plain
          @click.prevent="handleClose"
        >
          取消
        </el-button>
      </el-form-item>
    </el-form>
    
  </el-drawer>
</template>

<script setup>
import { updateUserInfo, getCCSTree } from '@/api/customer'
import useUserStore from '@/store/modules/user'
import ImageCropper from '@/components/ImageCropper'
import { realNamePattern } from '@/utils'

const { proxy } = getCurrentInstance();
const userStore = useUserStore()

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  }
});

const loading = ref(false);
const ccsList = ref([]);

const form = ref({
  nickName: '',
  realName: '',
  avatar: '',
  standardTypeCodeGbs: '',
  standardTypeCodeGbsList: [],
});
const rules = ref({
  nickName: [
    { required: true, message: "昵称不能为空", trigger: "blur" },
    { min: 2, max: 20, message: "昵称长度必须介于 2 和 20 之间", trigger: "blur" }
  ],
  realName: [
    { min: 2, max: 20, message: "真实名称长度必须介于 2 和 20 之间", trigger: "blur" },
    { pattern: realNamePattern, message: "真实名称2-20个字母、汉字", trigger: "blur" },
  ],
});

const emit = defineEmits(['update:open','success']);

const initData = () => {
  form.value.nickName = userStore.nickName;
  form.value.realName = userStore.userInfo.realName || '';
  form.value.avatar = userStore.userInfo.avatar;
  form.value.standardTypeCodeGbsList = userStore.userInfo.standardTypeCodeGbsList || [];
};

const handleClose = () => {
  emit('update:open', false);
};

const handleSave = () => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      if(form.value.standardTypeCodeGbsList.length == 0) {
        form.value.standardTypeCodeGbs = ''
      }else{
        form.value.standardTypeCodeGbs = form.value.standardTypeCodeGbsList.join(',');
      }
      updateUserInfo(form.value).then(res => {
        proxy.$modal.msgSuccess('账户信息设置成功！');
        emit('success');
        emit('update:open', false);
      }).finally(() => {
        loading.value = false;
      });
    }
  });
}

const getCCS = () => {
  getCCSTree().then(res => {
    ccsList.value = res.data || [];
  })
}

initData()
getCCS()

</script>
<style lang="scss" scoped>
:deep(.el-tag.el-tag--info){
  color: $primary-color;
  background-color: #DBE8FF;
}
:deep(.el-tag .el-icon){
  color: #999999;
  :hover{
    color: #FFFFFF;
    background-color: $primary-color;
    border-radius: 50%;
  }
}
</style>