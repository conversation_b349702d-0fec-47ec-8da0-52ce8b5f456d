import { getCode } from '@/api/login';

export default () => {
  let timer;
  let newTimer; // 针对同一个页面同时需要2个倒计时的情况
  const time = ref(0);
  const newTime = ref(0);
  const codeCountDown = () => {
    timer = setInterval(() => {
      if (time.value > 1) {
        time.value--;
      } else {
        clearInterval(timer);
        timer = 0;
        time.value = 0;
      }
    }, 1000);
  }
  const newCodeCountDown = () => {
    newTimer = setInterval(() => {
      if (newTime.value > 1) {
        newTime.value--;
      } else {
        clearInterval(newTimer);
        newTimer = 0;
        newTime.value = 0;
      }
    }, 1000);
  }
  const getMobileCode = (phonenumber,checkType = 0) => {
    return new Promise((resolve, reject) => {
      if (!timer || timer == 0) {
        getCode({ phonenumber, checkType }).then((res) => {
          time.value = 60
          codeCountDown();
          resolve(res)
        }).catch((error) => {
          reject(error)
        });
      }else{
        reject()
      }
    })
  }
  const getNewMobileCode = (phonenumber,checkType = 0) => {
    return new Promise((resolve, reject) => {
      if (!newTimer || newTimer == 0) {
        getCode({ phonenumber, checkType }).then((res) => {
          newTime.value = 60
          newCodeCountDown();
          resolve(res)
        }).catch((error) => {
          reject(error)
        });
      }else{
        reject()
      }
    })
  }
  return {
    time,
    newTime,
    newCodeCountDown,
    codeCountDown,
    getMobileCode,
    getNewMobileCode
  }
}