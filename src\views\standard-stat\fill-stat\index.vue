<template>
  <div class="app-container">
    <div class="content">
      <img src="@/assets/images/standard-stat/title.png" class="content-title" />
      <el-input v-model="inputValue" maxlength="100" placeholder="请输入填报主题完整名称进行搜索" />
      <div>
        <el-button @click="handleDetail" type="primary">查询</el-button>
        <el-button @click="handleReset" :text="true" :bg="true">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getFillingData } from '@/api/standard-stat/fill-in';

  const router = useRouter();

  const inputValue = ref('');

  const handleDetail = () => {
    getFillingData({ fillingTheme: inputValue.value }).then(res => {
      let data = res.data
      router.push('/standard-stat/fill-stat/detail?id=' + data.id + '&fillingTheme=' + encodeURI(data.fillingTheme));
    });
  };

  const handleReset = () => {
    inputValue.value = '';
  };
</script>

<style lang="scss" scoped>
  .app-container {
    width: 100%;
    height: calc(100vh - 100px);
    background-image: url('@/assets/images/standard-stat/fill-bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .content {
    text-align: center;

    &-title {
      display: block;
      width: 394px;
      margin: 0 auto;
    }
  }

  :deep(.el-input) {
    width: 55vw !important;
    height: 60px !important;
    margin: 50px 0 70px 0;
  }

  :deep(.el-input__wrapper:hover) {
    box-shadow: none !important;
  }

  :deep(.el-input__wrapper.is-focus) {
    box-shadow: none !important;
    border: none !important;
  }

  :deep(.el-input__inner) {
    text-align: center !important;
  }

  :deep(.el-button) {
    width: 160px !important;
    height: 45px !important;
    line-height: 45px !important;
    font-size: 16px !important;
  }

  :deep(.el-button + .el-button) {
    margin-left: 30px;
  }
</style>
