<template>
  <el-dialog
    title="导入"
    width="520px"
    append-to-body
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :limit="1"
      :before-upload="handleBeforeUpload"
      :headers="upload.headers"
      :action="upload.url"
      :disabled="upload.isUploading"
      :on-progress="handleFileUploadProgress"
      :on-success="handleFileSuccess"
      :on-error="handleFileError"
      :auto-upload="true"
      :show-file-list="false"
    >
      <div class="content">
        <img :src="imgUrl" alt="" class="content-img pointer" />
        <div class="mt25">按导入模板要求填写导入标准，并上传文件</div>
      </div>
    </el-upload>
    <div class="flex flex-center mt20">
      <a href="/user-center/file/体系导入标准模板.xlsx" class="text-decoration pointer">下载导入模板</a>
    </div>
  </el-dialog>
</template>

<script setup>
  import { ElLoading } from 'element-plus';
  import { getToken } from '@/utils/auth';

  const { proxy } = getCurrentInstance();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    nodeId: [String, Number],
  });

  let downloadLoadingInstance;
  const imgUrl = new URL('@/assets/images/system/upload.png', import.meta.url).href;
  const fileSize = ref(100);
  const fileType = ref(['xlsx', 'xls']);
  const fileList = ref([]);
  const upload = ref({
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: process.env.VITE_APP_BASE_API + '/business/standardSystemPfNode/importStandard/' + props.nodeId,
  });

  const handleBeforeUpload = file => {
    let isValid = false;
    if (fileType.value.length) {
      let fileExtension = '';
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
      }
      isValid = fileType.value.some(type => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
    } else {
      isValid = file.type.indexOf('image') > -1;
    }
    if (!isValid) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${fileType.value.join('/')}格式文件!`);
      return false;
    }
    if (fileSize.value) {
      const isLt = file.size / 1024 / 1024 < fileSize.value;
      if (!isLt) {
        proxy.$modal.msgError(`上传文件大小不能超过 ${fileSize.value} MB!`);
        return false;
      }
    }
    return true;
  };

  const handleFileUploadProgress = (event, file, fileList) => {
    upload.value.isUploading = true;
    downloadLoadingInstance = ElLoading.service({ text: '正在上传数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
  };

  const handleFileSuccess = (response, file, fileList) => {
    upload.value.isUploading = false;
    proxy.$refs.uploadRef.clearFiles();
    downloadLoadingInstance.close();
    const { code, data, message } = response;
    if (code == 200) {
      emit('updateData', data);
      emit('update:visible', false);
    } else {
      proxy.$modal.msgError(message);
    }
  };

  const handleFileError = (error, uploadFile, uploadFiles) => {
    downloadLoadingInstance.close();
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'updateData']);
</script>

<style lang="scss" scoped>
  .content {
    width: 100%;
    font-size: 14px;
    color: #333;
    padding: 35px 20px 25px;
    box-sizing: border-box;
    background: #f4f7ff;
    border-radius: 3px;
    text-align: center;

    &-img {
      display: block;
      width: 50px;
      margin: 0 auto;
    }
  }

  .text-decoration {
    color: $primary-color;
    text-decoration: underline;
    margin-left: 5px;
  }

  :deep(.el-upload) {
    width: 100% !important;
  }
</style>
