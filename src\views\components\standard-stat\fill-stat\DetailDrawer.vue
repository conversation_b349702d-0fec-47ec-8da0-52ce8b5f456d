<template>
  <el-drawer title="详情" size="80%" append-to-body v-model="props.visible" :before-close="handleClose">
    <div class="content-type">
      <div class="flex flex-ai-center">
        <div
          @click="toggle(item.value)"
          v-for="(item, index) in typeList"
          :key="index"
          class="content-type-item"
          :class="item.value == activeIndex ? 'content-type-active' : ''"
        >
          {{ item.title }}
        </div>
      </div>
    </div>
    <template v-if="activeIndex == 0">
      <div class="h-title mt20">填表信息</div>
      <el-descriptions class="mt20" title="" :column="3">
        <el-descriptions-item label="填表人：">{{ form.fillingPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话：">{{ form.fillingPersonContact }}</el-descriptions-item>
        <el-descriptions-item label="填报主题：">{{ form.fillingTheme }}</el-descriptions-item>
        <el-descriptions-item label="发起部门：">{{ form.initiatingDepartment }}</el-descriptions-item>
        <el-descriptions-item label="提交时间：">{{ form.createTime }}</el-descriptions-item>
      </el-descriptions>
      <div class="h-title mt20">调查单位基本情况</div>
      <el-descriptions class="mt20" title="" :column="3">
        <el-descriptions-item label="企业名称：">{{ form.enterpriseName }}</el-descriptions-item>
        <el-descriptions-item label="统一社会信用代码：">{{ form.socialCode }}</el-descriptions-item>
        <el-descriptions-item label="法定代表人（单位负责人）：">{{ form.legalRepresent }}</el-descriptions-item>
        <el-descriptions-item label="联系电话：">{{ form.legalRepresentContact }}</el-descriptions-item>
        <el-descriptions-item label="标准化负责人（标准总监）：">{{ form.standardizationManager }}</el-descriptions-item>
        <el-descriptions-item label="联系电话：">{{ form.standardizationManagerContact }}</el-descriptions-item>
        <el-descriptions-item label="负责人电子邮箱：">{{ form.email }}</el-descriptions-item>
        <el-descriptions-item label="单位网址：">{{ form.unitWebsite }}</el-descriptions-item>
        <el-descriptions-item label="行业类别：">{{ form.nationalEconomicTypeName }}</el-descriptions-item>
        <el-descriptions-item label="行业代码：">{{ form.nationalEconomicTypeCode }}</el-descriptions-item>
        <el-descriptions-item label="产业类别：">{{ form.industryCategory }}</el-descriptions-item>
        <el-descriptions-item label="单位地址：">{{ form.unitAddressRegion }}{{ form.unitAddressDetail }}</el-descriptions-item>
        <el-descriptions-item label="主要产品（或主要业务活动）：">{{ form.mainProduct }}</el-descriptions-item>
      </el-descriptions>
    </template>
    <template v-else>
      <div class="h-title mt20">标准化组织投入情况</div>
      <div class="info">
        是否设置标准化部门：
        <span class="info-text">{{ form.isSetStandardizationDeptName }}</span>
      </div>
      <div class="info">
        标准化工作人员共：
        <span class="info-text">{{ form.standardizationStaffNum }}</span>
        人，其中，专职人员
        <span class="info-text">{{ form.fullTimeStaffNum }}</span>
        人，其中，兼职人员
        <span class="info-text">{{ form.partTimeStaffNum }}</span>
        人
      </div>
      <div class="info">
        标准化工程师共：
        <span class="info-text">{{ form.standardizationEngineerNum }}</span>
        人，其中，初级人员
        <span class="info-text">{{ form.juniorEngineerNum }}</span>
        人，其中，中级人员
        <span class="info-text">{{ form.intermediateEngineerNum }}</span>
        人
      </div>
      <div class="info">
        标准化经费投入（年度）共计：
        <span class="info-text">{{ form.standardizationPersonnelFunds }}</span>
        万元，其中，标准化人员经费投入
        <span class="info-text">{{ form.standardizationPersonnelFunds }}</span>
        万元，标准化项目（活动）经费投入
        <span class="info-text">{{ form.standardizationProjectFunds }}</span>
        万元
      </div>
      <div class="h-title mt40">标准化成果</div>
      <div class="info">
        参与标准化活动共：
        <span class="info-text">{{ form.standardizationActivityNum }}</span>
        项，其中，试点示范项目
        <span class="info-text">{{ form.pilotDemonstrationProjectNum }}</span>
        项，创新贡献奖
        <span class="info-text">{{ form.innovationContributionAwardNum }}</span>
        项，企业标准领跑者
        <span class="info-text">{{ form.enterpriseStandardLeaderNum }}</span>
        项，对标达产品
        <span class="info-text">{{ form.benchmarkingStandardProductsNum }}</span>
        项，其他
        <span class="info-text">{{ form.otherStandardizationActivityNum }}</span>
        项，其他项目名称
        <span class="info-text">{{ form.otherProjectName }}</span>
      </div>
      <div class="info">
        参加标准化技术组织名称
        <span class="info-text">{{ form.standardizationTechnicalOrganizationName }}</span>
      </div>
      <div class="info">
        参加人员（人）
        <span class="info-text">{{ form.participantNum }}</span>
      </div>
      <div class="h-title mt40">标准化荣誉</div>
      <el-table ref="tableRef" :data="form.honorList" class="mt20">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" label="序号" fixed="left" width="60" />
        <el-table-column prop="honorName" label="荣誉名称" fixed="left" min-width="200" show-overflow-tooltip />
        <el-table-column prop="obtainDate" label="获得日期" min-width="150" show-overflow-tooltip />
        <el-table-column label="附件" min-width="120">
          <template #default="{ row }">
            <template v-if="row.honorFileList && row.honorFileList.length > 0">
              <div class="flex flex-jc-center">
                <ele-upload-image v-model:value="row.honorFileList" :isShowUploadIcon="false"></ele-upload-image>
              </div>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div class="h-title mt40">主导参与制修订标准</div>
      <el-table ref="tableRef" :data="form.leadStandardList" class="mt20">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" label="序号" fixed="left" width="60" />
        <el-table-column prop="standardCode" label="标准号" fixed="left" min-width="180" show-overflow-tooltip />
        <el-table-column prop="standardName" label="标准名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="standardTypeName" label="标准类型" min-width="150" show-overflow-tooltip />
        <el-table-column prop="publishDate" label="发布年份" min-width="120" show-overflow-tooltip />
        <el-table-column prop="draftingUnitRankingName" label="起草单位排名" min-width="120" show-overflow-tooltip />
        <el-table-column label="标准状态" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="standardStatusColor(row.standardStatus)">
              {{ row.standardStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="采标情况" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="procurementStatusColor(row.procurementStatus)">
              {{ row.procurementStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="isPubliclyDisplayedName"
          label="是否在标准信息公共服务平台公示"
          min-width="240"
          show-overflow-tooltip
        />
      </el-table>
      <div class="h-title mt40">主要执行标准</div>
      <el-table ref="tableRef" :data="form.executionStandardList" class="mt20">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" label="序号" fixed="left" width="60" />
        <el-table-column prop="standardCode" label="标准号" fixed="left" min-width="180" show-overflow-tooltip />
        <el-table-column prop="standardName" label="标准名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="standardTypeName" label="标准类型" min-width="150" show-overflow-tooltip />
        <el-table-column prop="publishDate" label="发布年份" min-width="120" show-overflow-tooltip />
        <el-table-column prop="draftingUnitRankingName" label="起草单位排名" min-width="120" show-overflow-tooltip />
        <el-table-column label="标准状态" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="standardStatusColor(row.standardStatus)">
              {{ row.standardStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="采标情况" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="procurementStatusColor(row.procurementStatus)">
              {{ row.procurementStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="isPubliclyDisplayedName"
          label="是否在标准信息公共服务平台公示"
          min-width="240"
          show-overflow-tooltip
        />
      </el-table>
    </template>
  </el-drawer>
</template>

<script setup>
  import { getFillingDataDetail } from '@/api/standard-stat/fill-in';
  import EleUploadImage from '@/components/EleUploadImage';

  const props = defineProps({
    id: [Number, String],
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const typeList = reactive([
    { title: '调查企业基本信息', value: 0 },
    { title: '标准化工作填报信息', value: 1 },
  ]);
  const activeIndex = ref(0);
  const form = ref({});

  const standardStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-00AEFF';
        break;
      case 1:
        return 'status-04AE00';
        break;
      case 2:
        return 'status-045CFE';
        break;
      case 3:
        return 'status-999999';
        break;
      case 4:
        return 'status-FF0000';
        break;
      default:
        return 'status-045CFE';
        break;
    }
  };

  const procurementStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-FF0000';
      case 1:
        return 'status-04AE00';
      default:
        return 'status-04AE00';
    }
  };

  const getDetail = () => {
    getFillingDataDetail(props.id).then(res => {
      form.value = res.data || {};
    });
  };

  getDetail();

  const toggle = data => {
    activeIndex.value = data;
  };

  const handleClose = () => {
    emit('update:id', '');
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'update:id']);
</script>

<style lang="scss" scoped>
  .content {
    &-type {
      border-bottom: 1px solid #e8e8e8;

      &-item {
        padding: 0 0 15px;
        font-size: 16px;
        color: #333;
        cursor: pointer;
        border-bottom: 3px solid #fff;

        &:not(:first-child) {
          margin-left: 40px;
        }
      }

      &-active {
        font-weight: bold;
        color: $primary-color;
        border-bottom-color: $primary-color;
      }
    }
  }

  .info {
    font-size: 14px;
    color: #999999;
    line-height: 22px;
    margin-top: 15px;

    &-text {
      font-size: 16px;
      color: #0372ff;
    }
  }

  .h-title {
    font-size: 16px !important;
  }

  :deep(.el-descriptions__cell) {
    width: calc(100% / 3) !important;
  }
</style>
