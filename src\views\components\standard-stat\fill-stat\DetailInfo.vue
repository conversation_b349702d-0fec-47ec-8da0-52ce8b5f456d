<template>
  <div>
    <div class="search">
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="企业名称" prop="enterpriseName">
          <el-input v-model="queryParams.enterpriseName" placeholder="请输入企业名称" @keyup.enter="getData('pageNum')" />
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="socialCode">
          <el-input v-model="queryParams.socialCode" placeholder="请输入统一社会信用代码" @keyup.enter="getData('pageNum')" />
        </el-form-item>
        <el-form-item label="行业类别" prop="nationalEconomicTypeId">
          <el-cascader
            ref="cascaderRef"
            v-model="queryParams.nationalEconomicTypeId"
            :options="nationalEconomicTypeOptions"
            :props="{
              emitPath: false,
              label: 'name',
              value: 'id',
            }"
            clearable
            placeholder="请选择行业类别"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="提交日期" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="">
          <el-button @click="handleSearch" type="primary" icon="Search">查询</el-button>
          <el-button @click="handleReset" plain icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="container-bar">
        <div class="bar-right">
          <el-button @click="handleDownLoad">
            <i class="iconfont icon-xiazaimoban"></i>
            下载
          </el-button>
        </div>
      </div>
      <el-table v-loading="loading" ref="tableRef" :data="tableData" class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" label="序号" fixed="left" width="70">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="enterpriseName" label="企业名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="socialCode" label="统一社会信用代码" min-width="200" show-overflow-tooltip />
        <el-table-column prop="nationalEconomicTypeName" label="行业类别" min-width="180" show-overflow-tooltip />
        <el-table-column prop="fillingPerson" label="填表人" min-width="150" show-overflow-tooltip />
        <el-table-column prop="fillingPersonContact" label="联系电话" min-width="180" show-overflow-tooltip />
        <el-table-column prop="createTime" label="提交时间" min-width="180" show-overflow-tooltip />
        <el-table-column label="操作" min-width="120" fixed="right">
          <template #default="{ row }">
            <el-button @click="handleDetail(row)" type="primary" link>详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <detail-drawer v-if="detailDrawer" v-model:visible="detailDrawer" v-model:id="currentId" />
  </div>
</template>

<script setup>
  import { getFillingDataList } from '@/api/standard-stat/fill-in';
  import { getNationalEconomicTypeTree } from '@/api/collect';
  import DetailDrawer from '@/views/components/standard-stat/fill-stat/DetailDrawer.vue';

  const { proxy } = getCurrentInstance();
  const route = useRoute();

  const loading = ref(false);
  const dateRange = ref([]);
  const nationalEconomicTypeOptions = ref([]);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const currentId = ref();
  const detailDrawer = ref(false);

  const handleReset = () => {
    dateRange.value = [];
    proxy.resetForm('queryFormRef');
    handleSearch();
  };

  const handleSearch = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  const getSelect = () => {
    getNationalEconomicTypeTree().then(res => {
      nationalEconomicTypeOptions.value = res.data || [];
    });
  };

  const getList = () => {
    loading.value = true;
    queryParams.value.fillingTaskId = route.query.id;
    queryParams.value.startTime = dateRange.value && dateRange.value.length > 0 ? dateRange.value[0] : '';
    queryParams.value.endTime = dateRange.value && dateRange.value.length > 0 ? dateRange.value[1] : '';
    getFillingDataList(queryParams.value)
      .then(res => {
        tableData.value = res.rows || [];
        total.value = res.total || 0;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleDownLoad = () => {
    proxy.download('/business/fillingData/export', { ...queryParams.value }, `填报主题_${new Date().getTime()}.xlsx`);
  };

  const handleDetail = row => {
    currentId.value = row.id;
    detailDrawer.value = true;
  };

  handleSearch();
  getSelect();
</script>

<style lang="scss" scoped>
  .search {
    margin-top: 15px;
    background-color: #fff;
    padding: 25px 30px 0;
    box-sizing: border-box;

    :deep(.el-form-item) {
      width: calc(91% / 4) !important;
      margin: 0 3% 20px 0 !important;
      box-sizing: border-box !important;
      overflow: hidden !important;

      &:nth-child(4n) {
        margin-right: 0 !important;
      }
    }

    :deep(.el-form-item__content) {
      width: 100% !important;
      .el-input,
      .el-select,
      .el-cascader,
      .el-autocomplete {
        width: 100% !important;
      }

      .el-date-editor.el-input {
        width: 100% !important;
      }
    }
  }

  .app-container-content {
    padding: 15px 30px 25px !important;
    box-sizing: border-box !important;
  }
</style>
