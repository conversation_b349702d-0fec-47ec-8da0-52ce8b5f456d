<template>
  <div v-hasPermi="['message_center:list']">
    <el-popover
      :width="600"
      trigger="hover"
      v-model:visible="showMessage"
      :show-arrow="true"
      @show="getList"
    >
      <template #reference>
        <el-badge :value="messageStore.unReadCount" :hidden="messageStore.unReadCount == 0" :max="99" class="item">
          <el-icon size="26"><Bell /></el-icon>
        </el-badge>
      </template>
      <div class="message-wrap">
        <div class="flex mb10">
          <div class="c-33 f-bold f-18">未读消息</div>
          <div class="m-red f-bold ml10 f-18">({{ messageStore.unReadCount }})</div>
        </div>
        <template v-if="dataList && dataList.length > 0">
          <div @click="toDetail(item)" class="message-item flex flex-ai-center" v-for="item in dataList">
            <img v-if="item.messageType == 1" src="@/assets/images/personal/message-feedback.png" alt="">
            <img v-if="item.messageType == 0" src="@/assets/images/personal/message-stadard.png" alt="">
            <i v-if="item.messageType == 2" class="iconfont icon-shenpizhongxin f-24 c-36cb22"></i>
            <div class="ml15 flex-1 pointer overflow-ellipsis">
              <div class="title f-bold overflow-ellipsis">{{ item.messageTitle }}</div>
              <div class="f-14 overflow-ellipsis">{{ item.messageContent }}</div>
            </div>
          </div>
        </template>
        <el-empty v-else :image="EMPTYBOX" />
        <div class="flex flex-center mt15 c-2F5AFF f-14 pointer" @click="toMore">查看全部>></div>
      </div>
    </el-popover>
    
  </div>
  <div>
    <feedback-detail-dialog
      v-if="detailDialog"
      :id="currentId"
      v-model:dialogVisible="detailDialog"
    />
    <pop-apply-detail v-if="processDetailDialog" v-model:open="processDetailDialog" :id="currentId"/>
  </div>
</template>
<script setup>
import PopApplyDetail from '@/views/components/approve/PopApplyDetail'
import FeedbackDetailDialog from '@/views/components/standard_manage/feedback/FeedbackDetailDialog'
import { unReadCountTopAndCount,messagePublishReadTag } from '@/api/message/message'
import useMessageStore from '@/store/modules/message'
import EMPTYBOX from '@/assets/images/empty-box.png'

import { toRefs } from 'vue'

const messageStore = useMessageStore()
const router = useRouter();
const { proxy } = getCurrentInstance()


const state = reactive({
  showMessage:false,
  queryParams:{
    pageSize:5
  },
  dataList:[],
  detailDialog:false,
  processDetailDialog:false,
  currentId:null,
  params:{
    messageId:null
  }
});

const {showMessage,dataList,detailDialog,currentId,processDetailDialog} = toRefs(state)

const getList = () => {
  unReadCountTopAndCount(state.queryParams).then(res => {
    state.dataList = res.rows;
    messageStore.unReadCount = res.otherInfo.unReadCount;
  })
}

getList()

const toMore = () => {
  state.showMessage = false;
  proxy.$router.push("/message_center/message_center")
}

const toDetail = (item) => {
  state.showMessage = false;
  if(item.messageType == 1){
    state.currentId = item.dataId;
    state.detailDialog = true;
  }else if(item.messageType == 0){
    router.push({ path: '/standard-resource/standard-data/detail', query: { id: item.dataId, standardType: item.standardType }});  
  }else{
    if(item.recordDelFlag && item.recordDelFlag == '1'){
      proxy.$modal.msgError('该数据不存在或已被删除！')
    }else{
      state.currentId = item.dataId;
      state.processDetailDialog = true;
    }
  }
  state.params.messageId = item.messageId;
  //标记为已读
  messagePublishReadTag(state.params).then(res => {
    getList()
  })
}




</script>
<style lang="scss" scoped>
.item {
  margin-top: 10px;
  margin-right: 20px;
}
.message-item{
  box-sizing: border-box;
  padding: 12px 0;
  border-bottom: 1px solid #E8E8E8;
  img{
    height: 24px;
  }
}
:deep(.el-badge__content.is-fixed){
  top: 5px;
  right: calc(3px + var(--el-badge-size)/ 2);
}
:deep(.el-empty){
  padding: 10px;
}
.c-2F5AFF{
  color: #2F5AFF;
}
.c-36cb22{
  color: #36cb22;
}
</style>