import request from '@/utils/request';

// 列表
export const getSystemList = params => {
  return request({
    url: '/business/standardSystem/userSystemList',
    method: 'get',
    params,
  });
};

// 新增
export const addSystem = data => {
  return request({
    url: '/business/standardSystem',
    method: 'post',
    data,
  });
};

// 修改
export const updateSystem = data => {
  return request({
    url: '/business/standardSystem',
    method: 'put',
    data,
  });
};

// 详情
export const getSystemDetail = id => {
  return request({
    url: '/business/standardSystem/' + id,
    method: 'get',
  });
};

// 删除
export const deleteSystem = data => {
  return request({
    url: '/business/standardSystem/remove',
    method: 'delete',
    data,
  });
};

// 详情-树
export const getSystemTree = id => {
  return request({
    url: '/business/standardSystemPfNode/tree/' + id,
    method: 'get',
  });
};

// 详情-列表
export const getSystemTable = params => {
  return request({
    url: '/business/standardSystemPfNode/getStandardWithNodePage',
    method: 'get',
    params,
  });
};





// 查询标准体系节点树
export const getStandardSystemTree = systemId => {
  return request({
    url: '/business/standardSystemPfNode/tree/' + systemId,
    method: 'get',
  });
};

// 新增标准体系目录
export const addStandardSystemTree = data => {
  return request({
    url: '/business/standardSystemPfNode',
    method: 'post',
    data,
  });
};

// 重命名标准体系目录
export const updateStandardSystemTree = data => {
  return request({
    url: '/business/standardSystemPfNode',
    method: 'put',
    data,
  });
};

// 删除标准体系目录
export const deleteStandardSystemTree = (systemId, data) => {
  return request({
    url: '/business/standardSystemPfNode/' + systemId,
    method: 'delete',
    data,
  });
};

// CCS - 级联分页查询
export const getCcsStandardSystemItemList = params => {
  return request({
    url: '/business/standardSystemPfNode/getStandardTypeWithNodePage',
    method: 'get',
    params,
  });
};

// 标准 - 级联分页查询标准
export const getStandardSystemItemList = params => {
  return request({
    url: '/business/standardSystemPfNode/getStandardWithNodePage',
    method: 'get',
    params,
  });
};

// 获取标准体系详细信息
export const getStandardSystemItemDetail = id => {
  return request({
    url: '/business/standardSystemPfNode/' + id,
    method: 'get',
  });
};

// 移动标准体系
export const moveStandardSystemItem = data => {
  return request({
    url: '/business/standardSystemPfNode/moveStandardSystemPfNode',
    method: 'put',
    data,
  });
};

// 移出 - 根据标准ids从标准体系移出
export const removeStandardSystemItem = data => {
  return request({
    url: '/business/standardSystemPfNode/removeOut',
    method: 'delete',
    data,
  });
};

// 导入标准
export const importStandardSystemItem = data => {
  return request({
    url: '/business/standardSystemPfNode/addStandard',
    method: 'post',
    data,
  });
};

// 导入CCS分类
export const importCcsStandardSystemItem = data => {
  return request({
    url: '/business/standardSystemPfNode/addStandardType',
    method: 'post',
    data,
  });
};

// 分页查询所有标准列表
export const getStandardList = params => {
  return request({
    url: '/search/sdc/stdStandard/list',
    method: 'get',
    params,
  });
};

// CCS筛选搜索分页
export const getCcsList = params => {
  return request({
    url: '/business/ccsType/list',
    method: 'get',
    params,
  });
};

// 移出ccs - 根据标准类型ids从标准体系节点移出
export const removeCcsStandardSystemItem = data => {
  return request({
    url: '/business/standardSystemPfNode/removeOutType',
    method: 'delete',
    data,
  });
};
