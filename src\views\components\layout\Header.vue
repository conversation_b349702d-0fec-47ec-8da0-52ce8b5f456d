<template>
  <div class="header-wrap">
    <div class="header-container">
      <div class="header-left">
        <a href="/">
          <img src="@/assets/images/layout/logo.png" alt="">
        </a>
        
      </div>
      <div class="header-right">
        <!-- 应用 -->
        <HeaderApplication />
        <!-- 解决方案 -->
        <HeaderSolution />
        <div @click="handleBuild" class="mr50 pointer">会员</div>
        <div @click="handleBuild" class="mr50 pointer">数据服务</div>
        <!-- 支持与服务 -->
        <HeaderSupport />
        <template v-if="userStore.token">
          <!-- 通知信息 -->
          <HeaderNotice />
          <!-- 用户信息 -->
          <HeaderUser />
        </template>
        <template v-else>
          <RouterLink to="/login" class="header-login">登录</RouterLink>
          <RouterLink to="/register" class="header-register">立即注册</RouterLink>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup>
import HeaderApplication from '@/views/components/layout/HeaderApplication'
import HeaderSolution from '@/views/components/layout/HeaderSolution'
import HeaderSupport from '@/views/components/layout/HeaderSupport'
import HeaderNotice from '@/views/components/layout/HeaderNotice'
import HeaderUser from '@/views/components/layout/HeaderUser'
import useUserStore from '@/store/modules/user'

const { proxy } = getCurrentInstance()

const userStore = useUserStore()

const handleBuild = () => {
  proxy.$modal.msgWarning('正在建设中，敬请期待')
}

</script>