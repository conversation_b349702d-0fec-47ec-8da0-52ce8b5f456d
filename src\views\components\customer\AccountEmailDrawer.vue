<template>
  <el-drawer title="邮箱设置" size="480" append-to-body v-model="props.open" :close-on-click-modal="false" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="app-dialog">
      <el-form-item label="邮箱" prop="email">
        <el-input
          size="large"
          v-model="form.email"
          placeholder="请输入邮箱"
          maxlength="50"
        />
      </el-form-item>

      <el-form-item class="mt50">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          style="width:100%;"
          @click.prevent="handleSave"
        >
          <span v-if="!loading">保 存</span>
          <span v-else>保 存 中...</span>
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="loading"
          size="large"
          style="width:100%;"
          plain
          @click.prevent="handleClose"
        >
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script setup>
import { setEmail } from '@/api/customer'
import useUserStore from '@/store/modules/user'
import { emailValidPattern } from '@/utils';

const { proxy } = getCurrentInstance();
const userStore = useUserStore()

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
});

const loading = ref(false);

const form = ref({
  email: ''
});
const rules = ref({
  email: [{ required: true, pattern: emailValidPattern, message: '请选择正确的邮箱地址', trigger: 'blur' }],
});

const emit = defineEmits(['update:open','success']);

const initData = () => {
  form.value.email = userStore.email;
};
const handleSave = () => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      setEmail(form.value).then(res => {
        proxy.$modal.msgSuccess('邮箱设置成功！');
        emit('success');
        emit('update:open', false);
      }).finally(() => {
        loading.value = false;
      });
    }
  });
}

const handleClose = () => {
  emit('update:open', false);
};

initData();

</script>
