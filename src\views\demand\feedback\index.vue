<template>
  <div class="app-container bgc-ff">
    <div class="app-container-content">
      <div class="flex flex-sb">
        <div class="overview-wrap">
          <div>服务反馈：<span class="num">{{ statInfo.total || '0' }}</span></div>
          <div>待处理：<span class="num">{{ statInfo.pending || '0' }}</span></div>
          <div>处理中：<span class="num">{{ statInfo.processing || '0' }}</span></div>
          <div>已处理：<span class="num">{{ statInfo.processed || '0' }}</span></div>
          <div>已撤回：<span class="num">{{ statInfo.withdraw || '0' }}</span></div>
        </div>
        <el-button
          @click="handleFeedback"
          type="primary"
          class="iconfont icon-zixunguanli"
        >
          <span class="ml5">立即反馈</span>
        </el-button>
      </div>
      <el-table v-loading="loading" :data="dataList" :border="false" class="mt15">
        <template v-slot:empty>
          <bxc-empty class="mt30" />
        </template>
        <el-table-column label="序号" fixed width="90">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="feedbackTypeName" label="类别" width="180" show-overflow-tooltip />
        <el-table-column prop="name" label="联系人" width="180" show-overflow-tooltip />
        <el-table-column prop="phone" label="联系方式" width="150" show-overflow-tooltip />
        <el-table-column prop="unitName" label="单位名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="feedbackContent" label="反馈内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="反馈日期" width="180" show-overflow-tooltip />
        <el-table-column prop="processStatusName" label="处理进度" width="150" show-overflow-tooltip >
          <template #default="{ row }">
            <span :class="getProcessStatusClass(row.processStatus)">
              {{ row.processStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="{ row }">
            <el-button @click="handleDetail(row)" type="primary" link>
              详情
            </el-button>
            <el-button v-if="row.processStatus == '0'" @click="handleRevoke(row)" type="primary" link>
              撤回
            </el-button>
            <el-button v-if="row.processStatus == '3'" @click="handleDelete(row)" type="primary" link>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination 
        v-show="total > 0" 
        :total="total" 
        v-model:page="queryParams.pageNum" 
        v-model:limit="queryParams.pageSize" 
        @pagination="getList" />
    </div>

    <pop-feedback v-if="open" v-model:open="open" @success="getList" />
    <feedback-drawer v-if="openDetail" v-model:open="openDetail" :id="currentItem.id" />
  </div>
</template>
<script setup>
import { getFeedbackList, revokeFeedback, deleteFeedback } from '@/api/demand/feedback'
import PopFeedback from '@/views/components/demand/PopFeedback'
import FeedbackDrawer from '@/views/components/demand/FeedbackDrawer'
import { getProcessStatusClass } from '@/utils';

const { proxy } = getCurrentInstance();

const loading = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const dataList = ref([]);
const total = ref(0);
const statInfo = ref({});
const open  = ref(false);
const openDetail  = ref(false);
const currentItem = ref({});

const getList = () => {
  loading.value = true;
  getFeedbackList(queryParams.value).then(res => {
    dataList.value = res.rows || [];
    total.value = res.total || 0;
    statInfo.value = res.bean || {};
  }).finally(() => {
    loading.value = false;
  })
}

const handleFeedback = () => {
  open.value = true;
}
const handleDelete  =  (row) => {
  let tip = '确认删除当前反馈信息？'
  proxy.$confirm(tip, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deleteFeedback({ids: [row.id]}).then(res=>{
      proxy.$modal.msgSuccess('删除成功')
      getList()
    })
  }).catch(() => { })
}
const handleRevoke  =  (row) => {
  let tip = '确认撤回当前反馈信息？'
  proxy.$confirm(tip, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    revokeFeedback(row.id).then(res=>{
      proxy.$modal.msgSuccess('撤回成功')
      getList()
    })
  }).catch(() => { })
}
const handleDetail = (row) => {
  currentItem.value = row;
  openDetail.value = true;
}

getList()

</script>