<template>
  <div class="app-container">
    <el-tabs v-model="activeName" class="stat-tabs" @tab-click="handleClick">
      <el-tab-pane label="基础属性统计" name="base">
      </el-tab-pane>
      <el-tab-pane label="分类统计" name="cate">
      </el-tab-pane>
      <el-tab-pane label="行业统计" name="industy">
      </el-tab-pane>
      <el-tab-pane label="批准部门统计" name="dept">
      </el-tab-pane>
    </el-tabs>

    <div class="stat-content">
      <template v-if="activeName == 'base'">
        <div class="stat-two-column">
          <standard-status-stat standardType="1" class="half-column" />
        </div>
      </template>
      <template v-if="activeName == 'cate'">
        <div class="stat-one-column">
          <standard-ne-category-stat standardType="1" class="flex-1" />
        </div>
        <div class="stat-one-column">
          <standard-ccs-stat standardType="1" class="flex-1" />
        </div>
        <div class="stat-one-column">
          <standard-ics-stat standardType="1" class="flex-1" />
        </div>
      </template>
      <template v-if="activeName == 'industy'">
        <div class="stat-one-column">
          <industry-category-stat class="flex-1" />
        </div>
      </template>
      <template v-if="activeName == 'dept'">
        <div class="stat-one-column">
          <standard-dept-stat class="flex-1" />
        </div>
      </template>
    </div>
  </div>
  </template>
  <script setup>
  import StandardStatusStat from '@/views/components/standard-stat/data-stat/StandardStatusStat.vue'
  import IndustryCategoryStat from '@/views/components/standard-stat/data-stat/IndustryCategoryStat.vue'
  import StandardDeptStat from '@/views/components/standard-stat/data-stat/StandardDeptStat.vue'
  import StandardIcsStat from '@/views/components/standard-stat/data-stat/StandardIcsStat.vue'
  import StandardCcsStat from '@/views/components/standard-stat/data-stat/StandardCcsStat.vue'
  import StandardNeCategoryStat from '@/views/components/standard-stat/data-stat/StandardNeCategoryStat.vue'

  const activeName = ref('base')
  
  const handleClick = (tab) => {
    
  }
  </script>
  <style lang="scss" scoped>

  </style>