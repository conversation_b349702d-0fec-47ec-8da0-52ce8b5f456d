<template>
  <div>
    <el-dialog title="" width="260px" v-model="visible" :modal="false" :lock-scroll="false" :before-close="handleClose">
      <div class="container">
        <span class="iconfont icon-chenggongtishicopy f-22 mr15"></span>
        {{ title }}
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '操作成功！',
    },
  });
  const { visible, title } = toRefs(props);

  const data = reactive({
    time: 2,
    timer: null,
  });
  const { time, timer } = toRefs(data);

  const countDown = () => {
    timer.value = setInterval(() => {
      if (time.value > 1) {
        time.value--;
      } else {
        handleClose();
      }
    }, 1000);
  };

  const emit = defineEmits(['update:visible']);

  const handleClose = () => {
    clearInterval(timer.value);
    emit('update:visible', false);
  };

  countDown();
</script>

<style lang="scss" scoped>
  .container {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2cba00;
    background-color: #f2fff4;
    font-size: 16px;
    line-height: normal !important;
    border: 1px solid #2cba00;
    border-radius: 5px;
  }

  :deep(.el-dialog__header) {
    display: none !important;
  }

  :deep(.el-dialog__body) {
    padding: 0 !important;
    overflow: auto !important;
    background-color: rgba(0, 0, 0, 0);
  }
</style>
