import request from '@/utils/request'

// 查询需求咨询信息列表
export function getConsultList(params) {
  return request({
    url: '/business/demandMessageInfo/list',
    method: 'get',
    params,
  });
}
// 获取需求咨询信息详细信息
export function getConsultDetail(id) {
  return request({
    url: '/business/demandMessageInfo/'+ id,
    method: 'get'
  });
}
// 新增需求咨询信息
export function addConsult(data) {
  return request({
    url: '/business/demandMessageInfo',
    method: 'post',
    data,
  });
}
// 撤回需求咨询信息
export function revokeConsult(id) {
  return request({
    url: '/business/demandMessageInfo/withdraw/'+ id,
    method: 'put'
  });
}
// 删除需求咨询信息
export function deleteConsult(data) {
  return request({
    url: '/business/demandMessageInfo/remove',
    method: 'delete',
    data,
  });
}