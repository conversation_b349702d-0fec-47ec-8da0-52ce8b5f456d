<template>
<div class="app-container">
  <div class="stat-list">
    <div v-for="item in statList" :key="item.path" @click="handleStat(item)" class="stat-item">
      <div class="stat-name">{{item.name}}</div>
      <div class="stat-btn">立即查看</div>
    </div>
  </div>
</div>
</template>
<script setup>
import router from '@/router';

const statList = [
  {
    name: '国家标准统计',
    path: '/standard-stat/data-stat/gb'
  },
  {
    name: '行业标准统计',
    path: '/standard-stat/data-stat/hb'
  },
  {
    name: '地方标准统计',
    path: '/standard-stat/data-stat/db'
  },
  {
    name: '团体标准统计',
    path: '/standard-stat/data-stat/tb'
  },
  {
    name: '企业标准统计',
    path: '/standard-stat/data-stat/qb'
  },
]
const handleStat = item => {
  router.push(item.path);
}
</script>
<style lang="scss" scoped>
.stat-list{
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  background: #FFFFFF;
  border: 1px solid #F1F1F1;
  padding: 28px;
  box-sizing: border-box;
  .stat-item{
    width: 300px;
    height: 130px;
    background: url('@/assets/images/standard-stat/stat-bg.png') no-repeat center;
    background-size: 100% 100%;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    justify-content: center;
    padding: 0 25px;
    &:hover{
      .stat-btn{
        background: #FFFFFF;
        border-radius: 3px;
        color: $primary-color;
      }
    }
    .stat-name{
      font-weight: bold;
      font-size: 18px;
      color: #FFFFFF;
    }
    .stat-btn{
      margin-top: 15px;
      width: 80px;
      height: 30px;
      background: #699EFF;
      border-radius: 3px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #FFFFFF;
    }
  }
}
</style>