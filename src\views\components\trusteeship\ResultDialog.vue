<template>
  <el-dialog
    width="470"
    title="托管结果"
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="flex flex-center mt10">
      <span class="iconfont icon-dui f-20 m-green mr10"></span>
      <span class="f-18 c-33 f-bold">批量托管成功</span>
    </div>
    <div class="flex flex-center f-14 c-33 content">
      <div>
        托管标准：
        <span class="c-primary">{{ resultData.totalCount || 0 }}</span>
      </div>
      <div class="ml20">
        成功托管：
        <span class="c-primary">{{ resultData.onCount || 0 }}</span>
      </div>
      <div class="ml20">
        托管失败：
        <span class="c-primary">{{ resultData.nonCount || 0 }}</span>
      </div>
    </div>
    <div @click="handleDownload" v-if="resultData.nonCount != 0" class="flex flex-center mb10 pointer">
      <span class="iconfont icon-xiazaimoban f-19 c-primary mr5"></span>
      <span class="c-primary" style="text-decoration: underline">下载托管失败标准</span>
    </div>
  </el-dialog>
</template>

<script setup>
  import { json2excel } from '@/utils/formatExcel';

  const props = defineProps({
    resultData: {
      required: true,
      type: Object,
      default: () => {
        return {};
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const { resultData } = toRefs(props);

  const handleDownload = () => {
    let excelDatas = [
      {
        tHeader: ['标准号', '失败原因'],
        filterVal: ['code', 'errorInfo'],
        tableDatas: resultData.value.nonStandardList,
        sheetName: 'Sheet1',
      },
    ];
    let multiHeader = ['批量导入失败标准'];
    json2excel(excelDatas, multiHeader, '批量导入失败标准', true, 'xlsx');
  };

  const handleClose = () => {
    emit('updateData');
    emit('update:resultData', {});
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'update:resultData', 'updateData']);
</script>

<style lang="scss" scoped>
  .content {
    width: 100%;
    height: 45px;
    border-radius: 5px;
    background-color: #e8f2ff;
    margin: 30px 0 20px;
  }
</style>
