<template>
  <div v-resize="onResize" :class="className" :style="{ height: height, width: width }" />
</template>

<script setup>
  import * as echarts from 'echarts';
  // import resize from "@/views/dashboard/mixins/resize";

  const { proxy } = getCurrentInstance();
  // mixins: [resize],
  const props = defineProps({
    className: {
      type: String,
      default: 'chart',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    chartData: {
      type: Object,
      required: true,
    },
    colors: {
      type: Array,
      default: ['#F5960E', '#A9DA00', '#12CFE0', '#0272FF', '#FF6D9D', '#A9DA00', '#12CFE0', '#00B838', '#0272FF', '#449e2f']
    }
  });
  const { className, width, height, autoResize, chartData } = toRefs(props);

  const data = reactive({
    chart: null,
  });

  const { chart } = toRefs(data);

  watch(
    () => props.chartData,
    newVal => {
      setOptions(newVal);
    },
    { deep: true }
  );

  onMounted(() => {
    proxy.$nextTick(() => {
      initChart();
    });
  });
  onBeforeUnmount(() => {
    if (!data.chart) {
      return;
    }
    data.chart.dispose();
    data.chart = null;
  });

  const initChart = () => {
    data.chart = markRaw(echarts.init(proxy.$el));
    setOptions(props.chartData);
  };
  const setOptions = () => {
    let colors = props.colors;
    data.chart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{b} : {d}%',
      },
      title: {
        text: '',
        x: 'center',
        y: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'normal',
          color: '#FFFFFF',
        },
      },
      legend: {
        x: 'center',
        bottom: 0,
        itemWidth: 8,
        itemHeight: 8,
        icon: 'circle',
        itemGap: 10,
        textStyle: {
          fontSize: 14,
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['36%', '56%'],
          center: ['50%', '45%'],
          // roseType : 'area',
          color: colors,
          itemStyle: {
            normal: {},
          },
          data: props.chartData,
          labelLine: {
            normal: {
              show: true,
              length: 30,
              length2: 15,
              // lineStyle: {
              //     color: '#CCCCCC',
              //     width: 2
              // }
            },
          },
          label: {
            normal: {
              formatter: function (params) {
                if (params.percent == undefined) {
                  return params.name + '\n' + '0%';
                } else {
                  return params.name + '\n' + params.percent + '%';
                }
              },
              rich: {
                b: {
                  fontSize: 12,
                  // color: "#FFF",
                  align: 'left',
                  padding: 2,
                },
                d: {
                  fontSize: 12,
                  align: 'center',
                  padding: 2,
                },
              },
            },
          },
        },
      ],
    });
  };
  const onResize = () => {
    data.chart && data.chart.resize();
  };
</script>
