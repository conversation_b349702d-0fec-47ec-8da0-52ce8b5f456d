<template>
  <div class="pop-container">
    <el-dialog :append-to-body="true" v-model="open" width="600px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="app-dialog">
        <el-form-item label="反馈类别" prop="feedbackType">
          <el-select
            style="width: 100%;"
            v-model="form.feedbackType"
            placeholder="请选择反馈类别"
            clearable
          >
            <el-option
              v-for="dict in bxc_serve_feedback_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈内容" prop="feedbackContent">
          <el-input
            v-model="form.feedbackContent"
            type="textarea"
            :rows="5"
            placeholder="请输入反馈说明信息"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="附件" prop="feedbackFilesList">
          <ele-upload-image
            :responseFn="handleResponse"
            :limit="9"
            :multiple="true"
            :fileSize="5"
            :fileType="fileType"
            v-model:value="form.feedbackFilesList"
          >
          </ele-upload-image>
          <div style="width:100%;"></div>
          <span class="m-red lh20 mt10">支持格式：jpeg、jpg、png；单个文件大小不超过5MB；最多上传文件数：9</span>
        </el-form-item>
        <el-form-item label="单位名称" prop="unitName">
          <el-input
            v-model="form.unitName"
            placeholder="请输入单位名称"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item label="联系人" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入联系人"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入联系方式"
            maxlength="20"
          />
        </el-form-item>

        <div class="tip-box mt30">
          您也可以通过直接拨打我们的官方客服热线：<span class="mobile">************</span>，进行需求内容咨询。
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close" plain>关闭</el-button>
          <el-button @click.prevent="save" type="primary" :loading="loading">
            <span v-if="!loading">提交</span>
            <span v-else>提交中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { addFeedback } from '@/api/demand/feedback'
import useUserStore from '@/store/modules/user'
import { telMobileValidPattern } from '@/utils'
import EleUploadImage from "@/components/EleUploadImage"

const { proxy } = getCurrentInstance()
const userStore = useUserStore()
const { bxc_serve_feedback_type } = proxy.useDict('bxc_serve_feedback_type')

const props = defineProps({
  open: Boolean,
});
const { open } = toRefs(props)

const data = reactive({
  title: '服务反馈',
  loading: false,
  fileType: ['jpeg','jpg','png'],
})

const {title,loading,fileType} = toRefs(data)

const form = ref({
  feedbackType: undefined,
  feedbackContent: '',
  unitName: userStore.userInfo.unitName || '',
  name: userStore.userInfo.realName || '',
  phone: userStore.phonenumber || '',
  feedbackFilesList: []
});
const rules = ref({
  feedbackType: [{ required: true, message: '请选择反馈类别', trigger: 'change' }],
  feedbackContent: [{ required: true, message: '请输入需求说明', trigger: 'blur' }],
  name: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: telMobileValidPattern, message: '请输入正确的联系方式（手机号/座机号）', trigger: 'blur' },
  ],
});

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}
const handleResponse = (response, file, fileList) => {
  return {'id': response.data.id, 'url': response.data.url, 'name':response.data.name}
}
const save = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      data.loading = true
      addFeedback(form.value).then(response => {
        emit('update:open',false);
        emit('success');
        proxy.$modal.msgSuccess("您所提交的反馈信息已收到，我们将有专员进行处理！");
      }).finally(() => {
        data.loading = false;
      });
    }
  });
}

</script>