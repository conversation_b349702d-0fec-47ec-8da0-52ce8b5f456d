<template>
  <div class="app-container workbench">
    <!-- 工作台概览 -->
    <div class="workbench-box">
      <workbench-overview />
    </div>
    <!-- 标准收藏/热门工具 -->
    <div class="workbench-container mt20">
      <div class="left workbench-box h460">
        <div class="f-22 f-bold c-33">标准收藏</div>
        <workbench-standard-collect />
      </div>
      <div class="right workbench-box h460">
        <div class="f-22 f-bold c-33">热门工具</div>
        <workbench-tools />
      </div>
    </div>
    <!-- 标准托管/推荐标准 -->
    <div class="workbench-container mt20">
      <div class="left workbench-box h490">
        <div class="f-22 f-bold c-33">标准托管</div>
        <workbench-standard-hosting />
      </div>
      <div class="right workbench-box h490">
        <div class="f-22 f-bold c-33">推荐标准</div>
        <workbench-recommend />
      </div>
    </div>
  </div>
</template>

<script setup name="Index">
import WorkbenchOverview from '@/views/components/workbench/WorkbenchOverview.vue'
import WorkbenchStandardCollect from '@/views/components/workbench/WorkbenchStandardCollect.vue'
import WorkbenchTools from '@/views/components/workbench/WorkbenchTools.vue'
import WorkbenchStandardHosting from '@/views/components/workbench/WorkbenchStandardHosting.vue'
import WorkbenchRecommend from '@/views/components/workbench/WorkbenchRecommend.vue'

</script>

<style lang="scss" scoped>
.workbench-box{
  padding: 30px;
  background: #FFFFFF;
  border: 1px solid #F1F1F1;
}
.workbench-container{
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  gap: 20px;
  .left{
    width: calc((100% - 20px)/3*2);
  }
  .right{
    width: calc((100% - 20px)/3*1);
  }
}
.h460{
  height: 460px;
}
.h490{
  height: 490px;
}
</style>
