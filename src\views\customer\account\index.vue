<template>
  <div class="app-container">
    <div class="user-wrap">
      <img class="avatar" :src="userStore.avatar" alt="">
      <div class="user-info">
        <div class="item-info">
          <div class="item">登录账户：<span>{{ userStore.userName }}</span></div>
          <div class="item">关注领域：<span>{{ focusAreas }}</span></div>
        </div>
        <div class="item-info">
          <div class="item">昵称：<span>{{ userStore.nickName }}</span></div>
          <div class="item">注册日期：<span>{{ userStore.userInfo.createTime }}</span></div>
        </div>
        <div class="item-info">
          <div class="item">真实姓名：<span>{{ userStore.userInfo.realName }}</span></div>
          <div class="item">上次登录日期：<span>{{ userStore.userInfo.loginDate }}</span></div>
        </div>
      </div>
      <i class="iconfont icon-shezhi c-primary setting" @click="handleSetting" ></i>
    </div>
    <!-- 安全设置 -->
    <div class="op-wrap">
      <div class="f-20 f-bold c-333">安全设置</div>
      <div class="op-list">
        <div class="op-item">
          <img src="@/assets/images/user/password.png" alt="">
          <div class="op-content">
            <div class="op-title">修改密码</div>
            <div class="op-desc">登录标信查平台时所需要输入的密码，建设定期修改登录密码，以提高账户安全性</div>
          </div>
          <div class="op-info">
            已设置
          </div>
          <div class="op-btns" @click="handlePassword">
            <i class="iconfont icon-zhongmingming"></i>
            <span class="ml5">修改</span>
          </div>
        </div>
        <div class="op-item">
          <img src="@/assets/images/user/mobile.png" alt="">
          <div class="op-content">
            <div class="op-title">安全手机号</div>
            <div class="op-desc">用于接收通知消息、验证身份(如找回密码、变更安全设置等)，绑定后可支持手机验证码登录</div>
          </div>
          <div class="op-info">
            {{formatPhone(userStore.phonenumber)}}
          </div>
          <div class="op-btns" @click="handlePhone">
            <i class="iconfont icon-zhongmingming"></i>
            <span class="ml5">修改</span>
          </div>
        </div>
        <div class="op-item">
          <img src="@/assets/images/user/mail.png" alt="">
          <div class="op-content">
            <div class="op-title">邮箱绑定</div>
            <div class="op-desc">用于接收通知消息、验证身份等</div>
          </div>
          <div class="op-info">
            <span v-if="userStore.email">{{formatEmail(userStore.email)}}</span>
            <span v-else>未绑定</span>
          </div>
          <div class="op-btns" @click="handleEmail">
            <i class="iconfont icon-bangding"></i>
            <span class="ml5">绑定</span>
          </div>
        </div>
        <div class="op-item">
          <img src="@/assets/images/user/notice.png" alt="">
          <div class="op-content">
            <div class="op-title">通知设置</div>
            <div class="op-desc">用于接收平台重要数据信息的通知方式(如标准托管)，设置后可多渠道接收通知信息，避免信息遗漏</div>
          </div>
          <div class="op-info">
            
          </div>
          <div class="op-btns" @click="handleNotice">
            <i class="iconfont icon-shezhi"></i>
            <span class="ml5">设置</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 绑定邮箱 -->
    <account-email-drawer v-if="openEmail" v-model:open="openEmail" @success="refreshUserInfo" />
    <!-- 账号设置 -->
    <account-setting-drawer v-if="openSetting" v-model:open="openSetting" @success="refreshUserInfo" />
    <!-- 修改密码 -->
    <account-password-drawer v-if="openPassword" v-model:open="openPassword" @success="refreshUserInfo" />
    <!-- 安全手机号码 -->
    <account-phone-drawer v-if="openPhone" v-model:open="openPhone" @success="refreshUserInfo" />
    <!-- 通知设置 -->
    <account-notice-drawer v-if="openNotice" v-model:open="openNotice" />

  </div>
</template>
<script setup>
import AccountEmailDrawer from '@/views/components/customer/AccountEmailDrawer'
import AccountSettingDrawer from '@/views/components/customer/AccountSettingDrawer'
import AccountPasswordDrawer from '@/views/components/customer/AccountPasswordDrawer'
import AccountPhoneDrawer from '@/views/components/customer/AccountPhoneDrawer'
import AccountNoticeDrawer from '@/views/components/customer/AccountNoticeDrawer'
import useUserStore from '@/store/modules/user'
import { formatPhone } from '@/utils'

const openEmail = ref(false)
const openSetting = ref(false)
const openPassword = ref(false)
const openPhone = ref(false)
const openNotice = ref(false)

const userStore = useUserStore()

const focusAreas = computed(() => {
  const focus = userStore.userInfo.standardTypeCodeGbsNameList
  if(!focus || focus.length == 0){
    return ''
  }else{
    return userStore.userInfo.standardTypeCodeGbsNameList.join('；')
  }
})

const handleEmail = () => {
  openEmail.value = true
}
const handleSetting = () => {
  openSetting.value = true
}
const handlePassword = () => {
  openPassword.value = true
}
const handlePhone = () => {
  openPhone.value = true
}
const handleNotice = () => {
  openNotice.value = true
}

const refreshUserInfo = () => {
  userStore.getInfo()
}
const formatEmail = (email) => {
  if(!email) return ''
  const index = email.indexOf('@')
  if(index == -1) return email
  if(index < 4) {
    return email.substring(0,index) + '****' + email.substring(index)
  }else{
    return email.substring(0,2) + '****'+ email.substring(index-2,index) + email.substring(index)
  }
}
</script>
<style lang="scss" scoped>
.user-wrap{
  height: 190px;
  background: #FFFFFF;
  border: 1px solid #F1F1F1;
  padding: 0 30px;
  display: flex;
  align-items: center;
  position: relative;

  .avatar{
    width: 100px;
    height: 100px;
    border-radius: 50%;
  }
  .user-info{
    margin-left: 30px;
    flex: 1;
    display: flex;
    gap: 10px;
    overflow: hidden;
    .item-info{
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 30px;
      overflow: hidden;
      .item{
        flex: 1;
        font-size: 16px;
        color: #999999;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        overflow: hidden;
        span{
          flex: 1;
          color: #333333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .setting{
    position: absolute;
    top: 30px;
    right: 30px;
    cursor: pointer;

  }
}
.op-wrap{
  margin-top: 20px;
  padding: 30px 30px 0px 30px;
  background: #FFFFFF;
  border: 1px solid #F1F1F1;
  .op-list{
    margin: 0 10px 0 10px;
    .op-item{
      height: 120px;
      display: flex;
      align-items: center;
      gap: 30px;
      font-size: 14px;
      border-bottom: 1px solid #E8E8E8;
      &:last-child{
        border-bottom: none;
      }

      img{
        width: 20px;
      }
      .op-content{
        flex: 1;
        display: flex;
        flex-direction: column;
        .op-title{
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
        .op-desc{
          margin-top: 10px;
          color: #999999;
        }
      }
      .op-info{
        width: 160px;
        color: #999999;
      }
      .op-btns{
        margin-left: 10px;
        display: flex;
        align-items: center;
        cursor: pointer;

        color: $primary-color;
      }
    }
  }
}
</style>