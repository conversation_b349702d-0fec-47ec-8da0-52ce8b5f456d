<template>
  <el-drawer title="反馈详情" size="960" append-to-body v-model="props.open" :before-close="handleClose">
    <div class="flex flex-sb f-14 mt10">
      <div class="flex flex-1">
        <div class="c-99 flex-shrink">反馈类别：</div>
        <div class="c-33">{{data.feedbackTypeName}}</div>
      </div>
      <div class="flex flex-1">
        <div class="c-99 flex-shrink">联系人：</div>
        <div class="c-33">{{data.name}}</div>
      </div>
    </div>
    <div class="flex flex-sb f-14 mt20">
      <div class="flex flex-1">
        <div class="c-99 flex-shrink">联系方式：</div>
        <div class="c-33">{{data.phone}}</div>
      </div>
      <div class="flex flex-1">
        <div class="c-99 flex-shrink">单位名称：</div>
        <div class="c-33">{{data.unitName}}</div>
      </div>
    </div>
    <div class="flex flex-sb f-14 mt20">
      <div class="flex flex-1">
        <div class="c-99 flex-shrink">处理进度：</div>
        <div :class="getProcessStatusClass(data.processStatus)">{{data.processStatusName}}</div>
      </div>
      <div class="flex flex-1">
        <div class="c-99 flex-shrink">反馈时间：</div>
        <div class="c-33">{{data.createTime}}</div>
      </div>
    </div>
    <div class="flex flex-sb f-14 mt20">
      <div class="flex flex-1">
        <div class="c-99 flex-shrink">反馈内容：</div>
        <div class="flex flex-column">
          <div class="c-33">{{data.feedbackContent}}</div>
          <ele-upload-image
            class="mt15"
            :limit="9"
            :multiple="true"
            :fileSize="5"
            :isShowUploadIcon="false"
            v-model:value="data.feedbackFilesList"
          >
          </ele-upload-image>
        </div>
      </div>
    </div>
    <template v-if="data.processStatus == '2'">
      <div class="flex flex-sb f-14 mt20">
        <div class="flex flex-1">
          <div class="c-99 flex-shrink">回复时间：</div>
          <div class="c-33">{{data.processTime}}</div>
        </div>
      </div>
      <div class="flex flex-sb f-14 mt20">
        <div class="flex flex-1">
          <div class="c-99 flex-shrink">回复说明：</div>
          <div class="c-33">{{data.processReply}}</div>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { getFeedbackDetail } from '@/api/demand/feedback'
import { getProcessStatusClass } from '@/utils';
import EleUploadImage from "@/components/EleUploadImage"

const { proxy } = getCurrentInstance();

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
  }
});

const loading = ref(false);
const data = ref({});

const emit = defineEmits(['update:open']);

const getData = () => {
  loading.value = true;
  getFeedbackDetail(props.id).then(res=>{
    data.value = res.data || {};
  }).finally(()=>{loading.value = false;})
};

const handleClose = () => {
  emit('update:open', false);
};

getData()
</script>
