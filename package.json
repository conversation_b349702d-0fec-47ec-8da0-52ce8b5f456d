{"name": "BxcDataWebCloudUser", "version": "1.0.0", "description": "标信查云平台用户中心", "author": "标信查", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@highlightjs/vue-plugin": "^2.1.0", "@netless/xml-js": "^1.6.16", "@tinymce/tinymce-vue": "5.0.0", "@vueuse/core": "9.5.0", "axios": "0.27.2", "bpmn-js-token-simulation": "^0.10.0", "diagram-js": "^11.6.0", "echarts": "5.4.3", "element-plus": "2.2.27", "file-saver": "^2.0.5", "fuse.js": "6.6.2", "highlight.js": "^11.8.0", "js-cookie": "3.0.1", "js-md5": "^0.7.3", "jsencrypt": "3.3.1", "min-dash": "^4.1.1", "nprogress": "0.2.0", "pinia": "2.0.22", "tinymce": "^5.10.2", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-image-crop-upload": "^3.0.3", "vue-router": "4.1.4", "xlsx": "^0.18.5", "xlsx-style-vite": "^0.0.2", "xml-js": "npm:@netless/xml-js"}, "devDependencies": {"@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "bpmn-js": "^8.9.0", "bpmn-js-properties-panel": "^0.46.0", "crypto-js": "^4.1.1", "sass": "1.56.1", "unplugin-auto-import": "0.11.4", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}}