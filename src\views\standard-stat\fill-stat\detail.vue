<template>
  <div class="app-container">
    <div class="info">
      <div class="info-title">{{ route.query.fillingTheme ? decodeURI(route.query.fillingTheme) : '-' }}</div>
      <div class="flex">
        <span
          @click="handleTab(index)"
          v-for="(item, index) in tabList"
          :key="index"
          class="info-tab"
          :class="activeIndex == index ? 'info-isactive' : ''"
        >
          {{ item.name }}
        </span>
      </div>
    </div>
    <detail-statistics v-if="activeIndex == 0" />
    <detail-info v-if="activeIndex == 1" />
  </div>
</template>

<script setup>
  import DetailStatistics from '@/views/components/standard-stat/fill-stat/DetailStatistics.vue';
  import DetailInfo from '@/views/components/standard-stat/fill-stat/DetailInfo.vue';

  const route = useRoute();

  const activeIndex = ref(0);
  const tabList = ref([{ name: '查看统计' }, { name: '查看详情' }]);

  const handleTab = index => {
    activeIndex.value = index;
  };
</script>

<style lang="scss" scoped>
  .info {
    width: 100%;
    background-color: #fff;
    padding: 40px 25px 27px;
    box-sizing: border-box;

    &-title {
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      text-align: center;
    }

    &-tab {
      position: relative;
      display: inline-block;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      cursor: pointer;

      &:not(:first-child) {
        margin-left: 60px;
      }
    }

    &-isactive {
      font-weight: 600 !important;
      color: #045cff !important;

      &:after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 50%;
        height: 3px;
        border-radius: 2px;
        background-color: #045cff;
      }
    }
  }
</style>
