<template>
  <div class="hosting-container mt20">
    <div class="overview-wrap">
      <div>即将实施：<span class="num">{{beanInfo.beExecuteCount || 0}}</span></div>
      <div>即将废止：<span class="num">{{beanInfo.beRepealCount || 0}}</span></div>
      <div>即将被替代：<span class="num">{{beanInfo.beReplaceCount || 0}}</span></div>
    </div>
    <el-table
      class="mt15"
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="false"
      highlight-current-row
    >
      <template v-slot:empty>
        <bxc-empty class="mt10" />
      </template>
      <el-table-column label="序号" fixed width="60">
        <template #default="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="标准号"
        min-width="100"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span @click="handleDetail(row)" class="c-primary pointer">{{ row.standardCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="standardName"
        label="标准名称"
        show-overflow-tooltip
      />
      <el-table-column
        prop="standardTypeName"
        label="标准类型"
        show-overflow-tooltip
      />
      <el-table-column label="标准状态" show-overflow-tooltip>
        <template #default="{ row }">
          <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="executeDate"
        label="实施日期"
        show-overflow-tooltip
      />
      <el-table-column
        prop="repealDate"
        label="废止日期"
        show-overflow-tooltip
      />
    </el-table>
  </div>
</template>
<script setup>
import { getTrusteeshipList } from '@/api/workbench'
import { getStatusColor } from '@/utils'

const loading = ref(false)
const dataList = ref([])
const total = ref(0)
const beanInfo = ref({})
const queryParams = ref({
  pageNum: 1,
  pageSize: 8
})

const getList = () => {
  loading.value = true
  getTrusteeshipList(queryParams.value).then(res => {
    dataList.value = res.rows || []
    total.value = res.total
    beanInfo.value = res.bean || {}
  }).finally(() => {
    loading.value = false
  })
}
const handleDetail = (row) => {
  window.open('/retrieval/domesticDetail?id=' + row.standardId)
}

getList()

</script>