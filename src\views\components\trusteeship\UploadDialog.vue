<template>
  <el-dialog
    title="批量托管"
    width="470"
    append-to-body
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :limit="1"
      :before-upload="handleBeforeUpload"
      :headers="upload.headers"
      :action="upload.url"
      :disabled="upload.isUploading"
      :on-progress="handleFileUploadProgress"
      :on-success="handleFileSuccess"
      :on-error="handleFileError"
      :auto-upload="true"
      :show-file-list="false"
    >
      <div class="content">
        <span class="iconfont icon-shangchuanshuju f-40 c-primary"></span>
        <div class="f-14 c-33 mt10">上传托管标准数据</div>
        <div class="f-14 c-99 mt10">请按批量托管导入模板说明正确填写托管标准数据</div>
      </div>
    </el-upload>
    <div class="flex flex-center mt20 mb20">
      <span class="iconfont icon-xiazaimoban f-19 c-primary"></span>
      <a href="/user-center/file/标准批量托管模板.xlsx" class="text-decoration pointer">下载批量托管导入模板</a>
    </div>
  </el-dialog>
</template>

<script setup>
  import { ElLoading } from 'element-plus';
  import { getToken } from '@/utils/auth';

  const { proxy } = getCurrentInstance();
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  let downloadLoadingInstance;
  const fileSize = ref(100);
  const fileType = ref(['xlsx', 'xls']);
  const fileList = ref([]);
  const upload = ref({
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: process.env.VITE_APP_BASE_API + '/search/trusteeshipManage/importTrusteeships',
  });

  const handleBeforeUpload = file => {
    let isValid = false;
    if (fileType.value.length) {
      let fileExtension = '';
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
      }
      isValid = fileType.value.some(type => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
    } else {
      isValid = file.type.indexOf('image') > -1;
    }
    if (!isValid) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${fileType.value.join('/')}格式文件!`);
      return false;
    }
    if (fileSize.value) {
      const isLt = file.size / 1024 / 1024 < fileSize.value;
      if (!isLt) {
        proxy.$modal.msgError(`上传文件大小不能超过 ${fileSize.value} MB!`);
        return false;
      }
    }
    return true;
  };

  const handleFileUploadProgress = (event, file, fileList) => {
    upload.value.isUploading = true;
    downloadLoadingInstance = ElLoading.service({ text: '正在上传数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
  };

  const handleFileSuccess = (response, file, fileList) => {
    upload.value.isUploading = false;
    proxy.$refs.uploadRef.clearFiles();
    downloadLoadingInstance.close();
    const { code, data, msg } = response;
    if (code == 200) {
      emit('uploadSuccess', data);
      emit('update:visible', false);
    } else {
      proxy.$modal.msgError(msg);
    }
  };

  const handleFileError = (error, uploadFile, uploadFiles) => {
    downloadLoadingInstance.close();
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'uploadSuccess']);
</script>

<style lang="scss" scoped>
  .content {
    width: 100%;
    font-size: 14px;
    color: #333;
    padding: 40px 0 35px;
    box-sizing: border-box;
    background: #f4f7ff;
    border-radius: 3px;
    text-align: center;
  }

  .text-decoration {
    color: $primary-color;
    text-decoration: underline;
    margin-left: 5px;
  }

  :deep(.el-upload) {
    width: 100% !important;
  }
</style>
