<template>
  <div class="app-container">
    <div class="standard-system-wrap">
      <div class="standard-system-left scroller-bar-style">
        <div v-showToolTip>
          <el-tooltip :content="systemItemInfo.systemName">
            <div class="f-18 f-bold c-33 mb10 overflow-ellipsis">
              {{ systemItemInfo.systemName }}
            </div>
          </el-tooltip>
        </div>
        <div v-showToolTip>
          <el-tooltip :content="systemItemInfo.systemDescription">
            <div class="f-14 f-bold c-33 mb10 overflow-ellipsis">
              {{ systemItemInfo.systemDescription }}
            </div>
          </el-tooltip>
        </div>
        <div class="flex flex-ai-center mt20">
          <el-input v-model="searchStr" placeholder="请输入节点名称" suffix-icon="Search" />
          <el-button type="primary" icon="Refresh" @click="searchStr = ''" class="ml10"></el-button>
        </div>
        <el-tree
          v-loading="menuLoading"
          class="mt40"
          :data="categoryList"
          ref="treeRef"
          node-key="id"
          empty-text="暂无数据"
          :highlight-current="true"
          :props="defaultProps"
          :expand-on-click-node="false"
          :default-expand-all="true"
          @node-click="handleNodeClick"
          :filter-node-method="filterNode"
        >
          <template #default="{ node, data }">
            <span class="custom-tree-node">
              <span class="custom-label">
                <suffix-icon class="mr5" fileType="0"></suffix-icon>
                <span v-showToolTip="['tree']">
                  <el-tooltip placement="bottom-start" :content="node.label">
                    <span>{{ node.label }}</span>
                  </el-tooltip>
                </span>
              </span>
            </span>
          </template>
        </el-tree>
      </div>
      <div class="standard-system-right scroller-bar-style">
        <div class="app-container-search br-15">
          <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
            <el-form-item label="标准号:" prop="standardCode">
              <el-input v-model="queryParams.standardCode" placeholder="请输入标准号" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="标准名称:" prop="standardName">
              <el-input v-model="queryParams.standardName" placeholder="请输入标准名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="标准类型:" prop="standardType">
              <el-select v-model="queryParams.standardType" placeholder="请选择标准类型" clearable>
                <el-option v-for="dict in bxc_standard_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="标准状态:" prop="standardStatus">
              <el-select v-model="queryParams.standardStatus" placeholder="请选择标准状态" clearable>
                <el-option v-for="dict in bxc_standard_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
              <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="app-container-content mt20 br-15">
          <div class="container-bar">
            <div class="bar-left">
              <div class="h-title">{{ currentMenu.name ? currentMenu.name : systemItemInfo.systemName }}</div>
            </div>
          </div>
          <div v-if="statInfo.total" class="flex flex-sb mt20">
            <div class="overview-wrap">
              <div v-if="statInfo.total">
                节点标准：
                <span class="num">{{ statInfo.total }}</span>
              </div>
              <div v-if="statInfo.countryStandard">
                国家标准：
                <span class="num">{{ statInfo.countryStandard }}</span>
              </div>
              <div v-if="statInfo.industryStandard">
                行业标准：
                <span class="num">{{ statInfo.industryStandard }}</span>
              </div>
              <div v-if="statInfo.localStandard">
                地方标准：
                <span class="num">{{ statInfo.localStandard }}</span>
              </div>
              <div v-if="statInfo.associationStandard">
                团体标准：
                <span class="num">{{ statInfo.associationStandard }}</span>
              </div>
              <div v-if="statInfo.enterpriseStandard">
                企业标准：
                <span class="num">{{ statInfo.enterpriseStandard }}</span>
              </div>
              <div v-if="statInfo.measurementTechnicalSpecification">
                计量技术法规：
                <span class="num">{{ statInfo.measurementTechnicalSpecification }}</span>
              </div>
              <div v-if="statInfo.normativeDocument">
                规范性文件：
                <span class="num">{{ statInfo.normativeDocument }}</span>
              </div>
            </div>
          </div>
          <el-table v-loading="loading" ref="tableRef" :data="dataList" class="mt20">
            <template v-slot:empty>
              <empty />
            </template>
            <el-table-column type="index" label="序号" fixed="left" min-width="55">
              <template #default="scope">
                {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="standardCode" label="标准号" width="200" fixed="left" show-overflow-tooltip>
              <template #default="{ row }">
                <span @click="handleJump(row)" class="c-primary pointer">{{ row.standardCode }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="standardName" label="标准名称" width="200" show-overflow-tooltip />
            <el-table-column prop="standardTypeName" label="标准类型" min-width="100" show-overflow-tooltip />
            <el-table-column prop="standardStatusName" label="标准状态" min-width="100" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="flex flex-jc-center">
                  <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
                  <el-tooltip
                    v-if="row.standardStatus == 4 && row.beReplacedStandardCode"
                    effect="dark"
                    :content="row.beReplacedStandardCode"
                  >
                    <el-icon class="ml5"><InfoFilled class="c-99" /></el-icon>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="publishDate" label="发布日期" min-width="180" show-overflow-tooltip />
            <el-table-column prop="executeDate" label="实施日期" min-width="180" show-overflow-tooltip />
            <el-table-column prop="nodeName" label="节点名称" min-width="200" show-overflow-tooltip />
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getStandardSystemTree, getStandardSystemItemList, moveStandardSystemItem } from '@/api/standard-system/my';
  import SuffixIcon from '@/components/SuffixIcon';

  const { proxy } = getCurrentInstance();
  const { bxc_standard_type, bxc_standard_status } = proxy.useDict('bxc_standard_type', 'bxc_standard_status');

  const loading = ref(false);
  const menuLoading = ref(false);
  const isDrag = ref(true);
  const currentMenu = ref({});
  const total = ref(0);
  const dataList = ref([]);
  const categoryList = ref([]);
  const searchStr = ref('');
  const systemId = ref(undefined);
  const systemItemInfo = ref({});
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const defaultProps = reactive({
    children: 'children',
    label: 'name',
  });
  const statInfo = ref({});

  isDrag.value = proxy.$auth.hasPermi('standard_application:item:drag');

  const getStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-045CFE';
      case 1:
        return 'status-04AE00';
      case 2:
        return '';
      case 3:
        return 'status-999999';
      case 4:
        return 'status-FF0000';
      default:
        return '';
    }
  };

  const resetQuery = () => {
    proxy.resetForm('queryFormRef');
    handleQuery();
  };

  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  const getList = () => {
    loading.value = true;
    getStandardSystemItemList(queryParams.value)
      .then(res => {
        dataList.value = res.rows || [];
        total.value = res.total || 0;
        statInfo.value = res.bean || {};
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleJump = row => {
    window.open(window.location.origin + '/retrieval/domesticDetail?id=' + row.standardId);
  };

  const handleNodeClick = (item, nodes) => {
    currentMenu.value = item;
    queryParams.value.nodeId = item.id;
    proxy.$nextTick(() => {
      proxy.$refs.treeRef.setCurrentKey(item.id);
      getList();
    });
  };

  const getCategoryTree = () => {
    menuLoading.value = true;
    getStandardSystemTree(systemId.value)
      .then(response => {
        if (response.data) {
          categoryList.value = response.data;
          systemItemInfo.value = response.otherInfo;
          if (!queryParams.value.nodeId && categoryList.value.length > 0) {
            queryParams.value.nodeId = categoryList.value[0].id;
            currentMenu.value = categoryList.value[0];
          }
          proxy.$nextTick(() => {
            proxy.$refs.treeRef.setCurrentKey(queryParams.value.nodeId);
            getList();
          });
        }
      })
      .finally(() => {
        menuLoading.value = false;
      });
  };

  watch(searchStr, val => {
    proxy.$refs.treeRef.filter(val);
  });

  /** 通过条件过滤节点  */
  const filterNode = (value, obj) => {
    if (!value) return true;
    return obj.name.indexOf(value) !== -1;
  };

  systemId.value = proxy.$route.query.id;

  getCategoryTree();
</script>

<style lang="scss" scoped>
  .standard-system-wrap {
    display: flex;
    height: calc(100vh - 126px);
    .standard-system-left {
      width: 430px;
      flex-shrink: 0;
      background: #ffffff;
      border-radius: 15px;
      overflow-y: auto;
      padding: 30px;
      box-sizing: border-box;

      :deep(.el-tree-node__content) {
        height: 40px !important;
        &:hover {
          background: #e9f0fe;
          border-radius: 5px;
          color: #3377ff;
        }
        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 16px;
          font-weight: bold;
          padding-right: 8px;
          height: 40px;
          overflow: hidden;
          .custom-label {
            flex: 1;
            white-space: nowrap;
            overflow: hidden; //文本超出隐藏
            text-overflow: ellipsis;
          }
        }
      }
      :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
        color: #3377ff;
      }
    }
    .standard-system-right {
      margin-left: 20px;
      flex: 1;
      background: #f3f6fd;
      overflow: auto;

      .app-container-content {
        .container-bar {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .tab-list {
            display: flex;
            .tab-item {
              padding: 10px;
              border-bottom: 1px solid #e8e8e8;
              font-size: 16px;
              cursor: pointer;
            }
            .active {
              color: $primary-color;
              font-weight: bold;
              border-bottom: 2px solid $primary-color;
            }
          }
        }
      }
    }
  }

  :deep(.el-button:focus) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:hover) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:active) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-form-item) {
    width: 340px !important;
  }
</style>
