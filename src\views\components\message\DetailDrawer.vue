<template>
  <el-drawer
    title="消息详情"
    size="70%"
    append-to-body
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="f-18 c-33 f-bold text-center">{{ form.messageTitle }}</div>
    <div class="f-14 c-99 text-center mt15">{{ form.createTime }}</div>
    <div class="line"></div>
    <div v-html="form.messageContent" class="f-14 c-33"></div>
    <template v-if="form.attachmentFileList && form.attachmentFileList.length > 0">
      <div class="h-title mt30">附件</div>
      <div class="flex flex-wrap">
        <div
          @click="handleDownload(item)"
          v-for="item in form.attachmentFileList"
          :key="item.id"
          class="f-14 c-primary mt20 underline-text pointer"
        >
          {{ item.name }}
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
  import { getMessageDetail } from '@/api/message/index';
  
  const router = useRouter();
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
  });

  const form = ref({});

  const getDetail = () => {
    getMessageDetail(props.id).then(res => {
      form.value = res.data;
    });
  };

  const handleDownload = row => {
    proxy.download('/resource/oss/download/' + row.id, {}, row.name);
  };

  const handleClose = () => {
    router.push('/message/index')
    emit('updateData');
    emit('update:id', null);
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'updateData', 'update:id']);

  getDetail();
</script>

<style lang="scss">
  .line {
    width: 100%;
    height: 1px;
    background-color: #e5e8ef;
    margin: 17px 0 25px;
  }

  .underline-text {
    text-decoration: underline;
  }
</style>
