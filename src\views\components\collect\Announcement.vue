<template>
  <div>
    <div v-if="props.total > 0" class="flex flex-sb mb15">
      <div class="overview-wrap">
        <div>
          收藏公告：
          <span class="num">{{ props.total }}</span>
        </div>
        <div v-if="bean.countryStandardNotice > 0">
          国家标准公告：
          <span class="num">{{ bean.countryStandardNotice }}</span>
        </div>
        <div v-if="bean.industryStandardNotice > 0">
          行业标准公告：
          <span class="num">{{ bean.industryStandardNotice }}</span>
        </div>
        <div v-if="bean.localStandardNotice > 0">
          地方标准公告：
          <span class="num">{{ bean.localStandardNotice }}</span>
        </div>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData" :border="false">
      <template v-slot:empty>
        <bxc-empty class="mt30" />
      </template>
      <el-table-column label="序号" fixed width="90">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="公告标题" min-width="200" fixed="left" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('jump', '/retrieval/announcementDetail?id=' + row.recordId)" class="c-primary pointer">
            {{ row.title }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="noticeCode" label="公告号" min-width="200" show-overflow-tooltip />
      <el-table-column prop="noticeTypeName" label="公告类型" min-width="180" show-overflow-tooltip />
      <el-table-column prop="stdNum" label="标准数量" min-width="180" show-overflow-tooltip />
      <el-table-column prop="publishDate" label="发布日期" min-width="180" show-overflow-tooltip />
      <el-table-column prop="publishingUnit" label="发布单位" min-width="180" show-overflow-tooltip />
      <el-table-column label="操作" min-width="100" fixed="right" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('cancel', row)" class="c-primary pointer">取消收藏</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
  import { upadteCollect } from '@/api/collect';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    bean: {
      type: Object,
      default: () => {
        return {};
      },
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    total: {
      type: [String, Number],
      default: 0,
    },
    queryParams: {
      type: Object,
    },
  });

  const { loading, bean, tableData, queryParams } = toRefs(props);

  const handleClick = (type, data) => {
    switch (type) {
      case 'jump':
        window.open(data, '_blank');
        break;
      case 'cancel':
        proxy
          .$confirm('确认取消收藏当前数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            upadteCollect({ id: data.id, recordId: data.recordId, recordType: 5, isCollect: 0 }).then(res => {
              proxy.$modal.msgSuccess('取消收藏成功！');
              emit('updateData');
            });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  const emit = defineEmits(['updateData']);
</script>

<style lang="scss" scoped></style>
