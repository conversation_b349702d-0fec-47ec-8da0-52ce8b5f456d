<template>
  <el-popover
    v-model="visible"
    popper-class="support-wrap"
    placement="bottom-start"
    trigger="hover"
    :offset="21"
    :show-arrow="false"
    ref="supportRef"
  >
    <div class="support-container scroller-bar-style">
      <div class="support-list">
        <dl v-for="(item,index) in dataList" :key="index">
          <dt>{{item.name}}</dt>
          <dd v-for="(row, i) in item.children" :key="i">
            <a class="row-title" @click.native.prevent="handleLink(row)">{{row.name}}</a>
          </dd>
        </dl>
        <dl>
          <dt>咨询服务</dt>
          <dd class="mt-10">
            <div class="f-20 c-33">400-109-7887</div>
            <div class="f-14 c-33 mt-10">微信客服</div>
            <img class="wx-service" src="@/assets/images/layout/wx-service.png" alt="">
          </dd>
        </dl>
      </div>
    </div>
    <template #reference>
      <div class="flex mr80">
        <div class="pointer">支持与服务</div>
        <el-icon class="ml5"><ArrowDown /></el-icon>
      </div>
    </template>
    <!-- 需求咨询 -->
    <pop-consult v-if="openConsult" v-model:open="openConsult" />
    <!-- 服务反馈 -->
    <pop-feedback v-if="openFeedback" v-model:open="openFeedback" />
  </el-popover>
  
</template>
<script setup>
import PopConsult from '@/views/components/demand/PopConsult.vue';
import PopFeedback from '@/views/components/demand/PopFeedback.vue';
import useUserStore from '@/store/modules/user'

const { proxy } = getCurrentInstance()
const userStore = useUserStore()
const supportRef = ref()
const visible = ref(false)
const openConsult = ref(false)
const openFeedback = ref(false)

const dataList = [
  {
    name: '需求&反馈',
    children: [{
      name: '需求咨询',
      path: '',
      type: 'consult'
    },{
      name: '服务反馈',
      path: '',
      type: 'feedback'
    }]
  },
  {
    name: '信息公告',
    children: [{
      name: '官网公告',
      path: '/notice',
    }]
  },
  {
    name: '服务帮助',
    children: [{
      name: '使用指南',
      path: '/guide',
    },{
      name: '服务条款',
      path: '/clause/service',
    },{
      name: '隐私政策',
      path: '/clause/privacy',
    }]
  },
]
const handleLink = (row) => {
  if(row.type == 'consult'){
    openConsult.value = true
    return
  }
  if(row.type == 'feedback'){
    openFeedback.value = true
    return
  }
  if(!row.path) {
    proxy.$modal.msgWarning('正在建设中，敬请期待')
  }else{
    location.href = row.path
  }
  supportRef.value.hide()
}
</script>
<style lang="scss">
.el-popover.el-popper.support-wrap {
  padding: 0px !important;
  margin: 0px !important;
  width: 100% !important;
  height: 360px !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
  // transform:translate(0px, 60px) !important;
  inset: 0px 0px auto -5px!important;

  .support-container {
    height: 360px;
    margin: 0 auto;
    width: 1200px;
    overflow-y: auto;
    .support-list {
      margin-top: 20px;
      display: flex;
      dl{
        flex: 0 0 20%;
        dt{
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
        dd{
          padding: 0;
          margin: 15px 0 0 0;
          font-size: 14px;
          color: #888888;
          .wx-service{
            width: 95px;
            height: 95px;
            margin-top: 10px;
          }
        }
        .row-title{
          color: #888888;
          &:hover{
            color: $primary-color;
            cursor: pointer;
          }
        }
      }
    }
  }
  
}
</style>