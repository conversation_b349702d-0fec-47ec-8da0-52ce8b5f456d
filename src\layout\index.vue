<template>
  <div class="app-wrap">
    <!-- <header class="nav">11</header> -->
    <Header />
    <sidebar class="sidebar-container" />
    <div class="content-wrap">
      <div :class="{ 'fixed-header': false }">
        <navbar />
      </div>
      <div class="main-container">
        <app-main />
      </div>
    </div>
  </div>
</template>

<script setup>
import Header from '@/views/components/layout/Header'
import Sidebar from './components/Sidebar/index.vue'
import { AppMain, Navbar } from './components'

</script>