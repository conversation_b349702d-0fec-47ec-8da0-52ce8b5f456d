import request from '@/utils/request';

// 标准状态统计
export function getStandardStatusStat(params) {
  return request({
    url: '/search/dataAnalysis/getStandardStatusStatistic',
    method: 'get',
    params,
  });
}
// 查询国民经济分类
export function getNECategory(params) {
  return request({
    url: '/search/dataAnalysis/netStatistic',
    method: 'get',
    params,
  });
}
// 查询ICS 分类统计
export function getICSCategory(params) {
  return request({
    url: '/search/dataAnalysis/icsStatistic',
    method: 'get',
    params,
  });
}
// 查询CCS 分类统计
export function getCCSCategory(params) {
  return request({
    url: '/search/dataAnalysis/ccsStatistic',
    method: 'get',
    params,
  });
}
// 标准类别统计
export function getStandardCategoryStat(params) {
  return request({
    url: '/search/dataAnalysis/getStandardCategoryStatistic',
    method: 'get',
    params,
  });
}
// 标准性质统计
export function getStandardAttrStat(params) {
  return request({
    url: '/search/dataAnalysis/getStandardAttrStatistic',
    method: 'get',
    params,
  });
}
// 行业领域统计
export function getIndustryStat(params) {
  return request({
    url: '/search/dataAnalysis/getIndustryCategoryStatistic',
    method: 'get',
    params,
  });
}
// 批准发布部门统计
export function getPublishDeptStat(params) {
  return request({
    url: '/search/dataAnalysis/getConfirmPublishDeptStatistic',
    method: 'get',
    params,
  });
}