<template>
  <el-popover
    v-model="visible"
    popper-class="nav-user-wrap"
    placement="bottom"
    trigger="hover"
    :offset="13"
    :show-arrow="false"
    ref="navUserRef"
  >
    <div class="nav-user-container scroller-bar-style">

      <div class="profile-wrap">
        <img :src="userStore.avatar" alt="">
        <div class="user-name">{{ userStore.nickName }}</div>
      </div>
      <div class="enter-center">
        <router-link to="/index">
          进入用户中心>>
        </router-link>
      </div>
      <div class="line"></div>
      <div class="tool-wrap">
        <div class="tool-title">常用工具</div>
        <div class="tool-list">
          <div v-for="(item,index) in dataList" :key="index" @click="handleLink(item)" class="tool-item">
            <i :class="item.icon"></i>
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
        <div class="flex flex-jc-end flex-ai-center c-88 f-14 mt10 pointer" @click="logout">
          <i class="iconfont icon-tuichudenglu"></i>
          <span class="ml5">退出登录</span>
        </div>
      </div>
    </div>
    <template #reference>
      <div class="flex">
        <img :src="userStore.avatar" alt="" class="header-avatar">
      </div>
    </template>
  </el-popover>
  
</template>
<script setup>
import useUserStore from '@/store/modules/user'
import { RouterLink } from 'vue-router';

const { proxy } = getCurrentInstance()
const userStore = useUserStore()
const navUserRef = ref()
const visible = ref(false)
const router = useRouter()

const dataList = [
  {
    name: '标准托管',
    icon: 'iconfont icon-kuangjituoguan',
    path: '/trusteeship/index',
  },
  {
    name: '我的收藏',
    icon: 'iconfont icon-shoucang2',
    path: '/collect/index',
  },
  {
    name: '订阅体系',
    icon: 'iconfont icon-yidingyue',
    path: '/standard-system/subscription',
  },
  {
    name: '我的体系',
    icon: 'iconfont icon-tixi',
    path: '/standard-system/my',
  },
]


const handleLink = (row) => {
  if(!row.path) {
    proxy.$modal.msgWarning('正在建设中，敬请期待')
  }else{
    router.push(row.path)
  }
  navUserRef.value.hide()
}
const logout = async () => {
  await userStore.logOut()
  location.href = '/login'
}


</script>
<style lang="scss">
.el-popover.el-popper.nav-user-wrap {
  padding: 0px !important;
  margin: 0px !important;
  width: 420px !important;
  // height: 288px !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
  // inset: 60px auto auto auto !important;

  .nav-user-container {
    // height: 288px;
    margin: 20px 20px 10px 20px;
    overflow-y: auto;
    .profile-wrap{
      display: flex;
      align-items: center;
      img{
        width: 51px;
        height: 51px;
        border-radius: 50%;
      }
      .user-name{
        margin-left: 10px;
        font-size: 16px;
      }
      
    }
    .enter-center{
      display: flex;
      justify-content: flex-end;
      color: $primary-color;
      margin: 20px 0px 8px 0px;
      a{
        cursor: pointer;
      }
    }
    .line{
      width: 100%;
      height: 1px;
      background: #E8E8E8;
      margin: 0px;
    }
    .tool-wrap{
      .tool-title{
        font-size: 16px;
        color: #333333;
        font-weight: bold;
        margin: 20px 0px;
      }
      .tool-list{
        height: 90px;
        background: #F8F9FB;
        padding: 0px 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .tool-item{
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;
          &:hover{
            .name{
              color: $primary-color;
              font-weight: bold;
            }
          }
          i{
            font-size: 22px;
            color: $primary-color;
          }
          .name{
            margin-top: 5px;
            font-size: 14px;
            font-weight: bold;
          }
        }
      }
    }
  }
  
}
</style>