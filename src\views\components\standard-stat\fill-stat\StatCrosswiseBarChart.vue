<template>
  <div v-resize="onResize" :class="className" :style="{height:height,width:width}" />
</template>

<script setup>
import * as echarts from 'echarts'
// import resize from '@/views/dashboard/mixins/resize'

const {proxy} = getCurrentInstance()
// mixins: [resize],
const props = defineProps({
  className: {
    type: String,
    default: 'chart'
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: "100%"
  },
  autoResize: {
    type: Boolean,
    default: true
  },
  chartData: {
    type: Object,
    required: true
  }
})
const {className,width,height,autoResize,chartData} = toRefs(props)

const data = reactive({
  chart: null
})

const {chart} = toRefs(data)

watch(()=>props.chartData,(newVal)=>{
  setOptions(newVal)
},{deep: true})

onMounted(()=>{
  proxy.$nextTick(() => {
    initChart()
  })
})
onBeforeUnmount(() => {
  if (!data.chart) {
    return
  }
  data.chart.dispose()
  data.chart = null
})

const initChart = () => {
  data.chart = markRaw(echarts.init(proxy.$el))
  setOptions(props.chartData)
}
const colorList=['#045CFF','#0E41FD','#6023DE','#BB20EB','#EBC30E','#FF8439'];

const setOptions = ({ dmList, nodeData } = {}) => {
  if(!dmList) return;
  let nums = nodeData[0].echartsData.map(item=>Number(item));
  let sumNum = Math.max(...nums);//nums.reduce(( acc, cur ) => acc + cur, 0)
  let sumData = Array.from({length: nums.length},() => sumNum)

  data.chart.setOption({
    tooltip: {
      trigger: 'axis',
      formatter: '{b} : {c}%'
    },
    grid: {
      left: 0,
      right: 0,
      bottom: 0,
      top: 0,
      containLabel: true
    },
    
    xAxis: {
      axisLabel: {
        show: true,
        textStyle: {
          fontSize: '14',
          color: '#666666'
        },
      },
      splitLine: {
        show: false
      },
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLabel: {
          show: true,
          textStyle: {
            fontSize: '14',
            color: '#666666'
          },
          formatter: function(value) {
            if (value !== undefined) {
              if (value.length >= 11) {      // 超出5个字符显示省略号
                  return `${value.slice(0, 11)}...`;
              }
              return value;
            }
          }
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E8E8E8'
          }
        },
        data: dmList
    }, 
    {
      type: 'category',
      inverse: true,
      axisTick: 'none',
      axisLine: 'none',
      show: false,
      axisLabel: {
        textStyle: {
          color: '#666666',
          fontSize: '14'
        },
        formatter: function(value) {
          return value ;
        },
      },
      data: nums
    }],
    series: [
      {
        name: '标准条数',
        type: 'bar',
        zlevel: 1,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
              offset: 0,
              color: '#045CFF' // 0% 处的颜色
            }, {
              offset: 0,
              color: '#00AEFF' // 0% 处的颜色
            }, {
              offset: 1,
              color: '#045CFF' // 100% 处的颜色
            }], false),
            barBorderRadius: [0, 30, 30, 0],
          }
        },
        barWidth: 14,
        data: nums
      },
      {
        // name: '背景',
        type: 'bar',
        barWidth: 14,
        barGap: '-100%',
        data: sumData,
        itemStyle: {
            normal: {
                color: '#ffffff',
            }
        },
      },
    ]
  })
}
const onResize = () => {
  data.chart && data.chart.resize()
}
</script>
