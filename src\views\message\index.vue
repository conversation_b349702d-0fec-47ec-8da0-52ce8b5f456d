<template>
  <div class="app-container">
    <div class="app-tabs bgc-ff">
      <div class="flex">
        <div @click="handleTabs(-1)" class="app-tabs-item" :class="{ 'app-tabs-active': activeTab == -1 }">全部消息</div>
        <div
          @click="handleTabs(item.value)"
          v-for="item in bxc_message_type"
          :key="item.value"
          class="app-tabs-item"
          :class="{ 'app-tabs-active': item.value == activeTab }"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="flex">
        <el-button @click="handleAllRead">
          <span class="iconfont icon-qingkong1 f-14 mr5"></span>
          全部已读
        </el-button>
        <el-button @click="isOperation = !isOperation" class="ml10">
          <span class="iconfont icon-piliangcaozuo f-14 mr5"></span>
          批量操作
        </el-button>
      </div>
    </div>
    <div class="app-container-content" :style="'padding-top:' + (isOperation ? '20px' : '0')">
      <div v-show="isOperation" class="tip">
        <el-checkbox :indeterminate="isIndeterminate" v-model="isAllSelected" @change="handleSelectAll"></el-checkbox>
        <div @click="handleSelectAll" class="ml10 pointer">全选</div>
        <div @click="handleDeleteAll" class="ml80 pointer">删除选中消息</div>
        <div @click="handleRead" class="ml50 pointer">已读选中消息</div>
      </div>
      <template v-if="tableData && tableData.length > 0">
        <el-checkbox-group v-model="selectedItems">
          <div @click.stop="handleDetail(item)" v-for="item in tableData" :key="item.messageId" class="card pointer">
            <div class="flex flex-ai-center" style="max-width: 80%">
              <el-checkbox @click.stop v-if="isOperation" :label="item.messageId" class="card-checkbox"></el-checkbox>
              <el-badge :is-dot="!item.readTag">
                <div class="card-icon"><span class="iconfont icon-xiaoxi c-ff f-18"></span></div>
              </el-badge>
              <div class="card-info">
                <div class="f-16 c-33 f-bold flex-1 overflow-ellipsis card-info-title">{{ item.messageTypeName }}</div>
                <div class="f-14 c-99 mt10 flex-1 overflow-ellipsis">{{ item.messageTitle }}</div>
              </div>
            </div>
            <div class="f-14 c-99 text-right">
              <div>{{ item.createTime }}</div>
              <el-icon @click.stop="handleDelete(item)" class="mt10 c-FF0000 f-20"><Delete /></el-icon>
            </div>
          </div>
        </el-checkbox-group>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getData"
        />
      </template>
      <bxc-empty v-else />
    </div>
    <detail-drawer v-if="detailVisible" v-model:visible="detailVisible" :id="currentId" @updateData="getData" />
  </div>
</template>

<script setup>
  import { getMessageAllList, putMessageStatus, deleteMessage } from '@/api/message/index';
  import DetailDrawer from '@/views/components/message/DetailDrawer.vue';

  const { proxy } = getCurrentInstance();
  const { bxc_message_type } = proxy.useDict('bxc_message_type');

  const loading = ref(false);
  const isOperation = ref(false);
  const activeTab = ref(-1);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const isAllSelected = ref(false);
  const isIndeterminate = ref(false);
  const selectedItems = ref([]);
  const currentId = ref();
  const detailVisible = ref(false);

  watch(
    () => selectedItems.value,
    newSelectedItems => {
      if (newSelectedItems.length == 0 || tableData.value.length == 0) {
        isAllSelected.value = false;
        isIndeterminate.value = false;
      } else if (newSelectedItems.length === tableData.value.length) {
        isAllSelected.value = true;
        isIndeterminate.value = false;
      } else if (newSelectedItems.length > 0) {
        isAllSelected.value = false;
        isIndeterminate.value = true;
      } else {
        isAllSelected.value = false;
        isIndeterminate.value = false;
      }
    },
    { immediate: true }
  );

  const getData = data => {
    loading.value = true;
    if (data) queryParams.value.pageNum = 1;
    queryParams.value.messageType = activeTab.value == -1 ? '' : activeTab.value;
    getMessageAllList(queryParams.value)
      .then(res => {
        tableData.value = res.rows || [];
        total.value = res.total || 0;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleTabs = index => {
    if (index == activeTab.value) return;
    activeTab.value = index;
    selectedItems.value = [];
    getData('pageNum');
  };

  const handleSelectAll = () => {
    if (isAllSelected.value) {
      selectedItems.value = tableData.value.map(item => item.messageId);
    } else {
      selectedItems.value = [];
    }
  };

  const handleAllRead = () => {
    proxy
      .$confirm('确认将全部标记消息设置为已读？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        putMessageStatus().then(res => {
          selectedItems.value = [];
          proxy.$modal.msgSuccess('已读设置成功！');
          getData('pageNum');
        });
      })
      .catch(() => {});
  };

  const handleDelete = row => {
    console.log(123);
    proxy
      .$confirm('确认删除这条站内消息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        deleteMessage({ ids: [row.messageId] }).then(res => {
          proxy.$modal.msgSuccess('删除成功！');
          getData('pageNum');
        });
      })
      .catch(() => {});
  };

  const handleDeleteAll = () => {
    if (selectedItems.value && selectedItems.value.length > 0) {
      proxy
        .$confirm('确认删除选中的站内消息吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          deleteMessage({ ids: selectedItems.value }).then(res => {
            selectedItems.value = [];
            proxy.$modal.msgSuccess('删除成功！');
            getData('pageNum');
          });
        })
        .catch(() => {});
    } else {
      proxy.$modal.msgWarning('请选择操作数据！');
    }
  };

  const handleRead = () => {
    if (selectedItems.value && selectedItems.value.length > 0) {
      proxy
        .$confirm('确认将选中的消息设置为已读？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        .then(() => {
          putMessageStatus({ messageId: selectedItems.value }).then(res => {
            selectedItems.value = [];
            proxy.$modal.msgSuccess('已读设置成功！');
            getData();
          });
        })
        .catch(() => {});
    } else {
      proxy.$modal.msgWarning('请选择操作数据！');
    }
  };

  const handleDetail = row => {
    currentId.value = row.messageId;
    detailVisible.value = true;
  };

  if (proxy.$route.query.id) {
    currentId.value = proxy.$route.query.id;
    detailVisible.value = true;
  }

  getData();
</script>

<style lang="scss" scoped>
  .app-tabs {
    padding: 5px 30px 0 10px;
    box-sizing: border-box;
  }

  .tip {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    background-color: #ebf2ff;
    padding: 0 20px;
    box-sizing: border-box;
    font-size: 14px;
    color: $primary-color;
    margin-bottom: 5px;
  }

  .card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
    padding: 20px 0;
    box-sizing: border-box;

    &:hover {
      .card-info-title {
        color: $primary-color !important;
      }
    }

    &-checkbox {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 20px;
    }

    &-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: $primary-color;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 20px;
    }

    &-info {
      flex: 1;
      margin-left: 20px;
      overflow: hidden;
    }
  }

  :deep(.el-button:focus) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:hover) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:active) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-badge__content.is-fixed.is-dot) {
    top: 3px;
    right: 10px;
  }

  :deep(.el-badge__content--danger) {
    width: 9px;
    height: 9px;
    color: #ff0000 !important;
    background-color: #ff0000 !important;
  }

  :deep(.el-checkbox-group) {
    line-height: 1;
  }

  :deep(.el-checkbox__label) {
    display: none;
  }
</style>
