import request from '@/utils/request'

// 查询报错管理信息列表
export function getErrorsList(params) {
  return request({
    url: '/business/errorManage/list',
    method: 'get',
    params,
  });
}
// 获取报错管理信息详细信息
export function getErrorsDetail(id) {
  return request({
    url: '/business/errorManage/'+ id,
    method: 'get'
  });
}
// 新增报错管理信息
export function addErrors(data) {
  return request({
    url: '/business/errorManage',
    method: 'post',
    data,
  });
}
// 撤回报错管理信息
export function revokeErrors(id) {
  return request({
    url: '/business/errorManage/withdraw/'+ id,
    method: 'put'
  });
}
// 删除报错管理信息
export function deleteErrors(data) {
  return request({
    url: '/business/errorManage/remove',
    method: 'delete',
    data,
  });
}