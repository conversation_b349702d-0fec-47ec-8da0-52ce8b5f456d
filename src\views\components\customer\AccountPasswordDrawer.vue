<template>
  <el-drawer title="登录密码设置" size="480" append-to-body v-model="props.open" :close-on-click-modal="false" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="app-dialog">
      <div class="mobile-box mt10">
        <div class="mobile-left">
          手机号码: <span>{{userStore.phonenumber}}</span>
        </div>
        <div class="mobile-right">
          <div v-if="!time ||time == 0" @click="handleVerify" class="code-box">获取验证码</div>
          <div v-else class="down-timer">{{time}}s 后重新获取</div>
        </div>
      </div>
      <el-form-item class="mt20" label="验证码" prop="smsCode">
        <el-input
          size="large"
          v-model="form.smsCode"
          placeholder="请输入验证码"
          auto-complete="off"
          maxlength="6"
        />
      </el-form-item>
      <el-form-item label="密码" prop="newPassword">
        <el-input
          v-model="form.newPassword"
          type="password"
          size="large"
          auto-complete="off"
          placeholder="请设置账户登录新密码"
          show-password
          maxlength="20"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="affirmUpwd">
        <el-input
          v-model="form.affirmUpwd"
          type="password"
          size="large"
          auto-complete="off"
          placeholder="确认登录密码"
          show-password
          maxlength="20"
        />
      </el-form-item>

      <el-form-item class="mt50">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          style="width:100%;"
          @click.prevent="handleSave"
        >
          <span v-if="!loading">保 存</span>
          <span v-else>保 存 中...</span>
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="loading"
          size="large"
          style="width:100%;"
          plain
          @click.prevent="handleClose"
        >
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script setup>
import { updatePassword } from '@/api/customer'
import useUserStore from '@/store/modules/user'
import { passwordValidPattern  } from '@/utils'
import useMobileCode from "@/composables/useMobileCode";

const { proxy } = getCurrentInstance();
const userStore = useUserStore()
const { time, getMobileCode } = useMobileCode()

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  }
});

const loading = ref(false);

const form = ref({
  newPassword: "",
  affirmUpwd: "",
  phonenumber: "",
  smsCode: "",
  resetType: '1'
});

const checkPassword = (rule, value, callback) => {
  if (value != form.value.newPassword) {
    callback(new Error("密码不一致"));
  } else {
    callback();
  }
}

const rules = ref({
  newPassword: [
    { required: true, trigger: "blur", message: "请输入设置登录密码" },
    { pattern: passwordValidPattern, trigger: "blur", message: "长度应在6～20位，须包含字母、数字、特殊符号至少两种，且非特殊符号开头" }
  ],
  affirmUpwd: [
    { required: true, trigger: "blur", message: "请输入确认登录密码" },
    { validator: checkPassword, trigger: "blur" },
  ],
  smsCode: [{ required: true, trigger: "blur", message: "请输入验证码" }],
});

const emit = defineEmits(['update:open','success']);

const handleVerify = () => {
  if(time.value == 0) {
    if (userStore.phonenumber) {
      let isSend = false; // 放重复点击
      if(isSend) return;
      
      getMobileCode(userStore.phonenumber, 4).then(res => {
        isSend = true;
      }).finally(() => {
        isSend = false;
      })
    }
  }
}
const initData = () => {
  form.value.phonenumber = userStore.phonenumber;
};
const handleSave = () => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      updatePassword(form.value).then(res => {
        proxy.$modal.msgSuccess('密码修改成功！');
        emit('success');
        emit('update:open', false);
      }).finally(() => {
        loading.value = false;
      });
    }
  });
}

const handleClose = () => {
  emit('update:open', false);
};

initData()

</script>
