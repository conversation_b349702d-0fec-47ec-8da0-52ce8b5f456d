<template>
  <div>
    <div v-if="props.total > 0" class="flex flex-sb mb15">
      <div class="overview-wrap">
        <div>
          收藏国外标准：
          <span class="num">{{ props.total }}</span>
        </div>
        <div v-if="bean.internationalStandard > 0">
          国际标准：
          <span class="num">{{ bean.internationalStandard }}</span>
        </div>
        <div v-if="bean.overseasStandard > 0">
          国外标准：
          <span class="num">{{ bean.overseasStandard }}</span>
        </div>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData" :border="false">
      <template v-slot:empty>
        <bxc-empty class="mt30" />
      </template>
      <el-table-column label="序号" fixed width="90">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="标准号" min-width="200" fixed="left" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('jump', '/retrieval/internationDetail?id=' + row.recordId)" class="c-primary pointer">
            {{ row.standardCode }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="standardName" label="标准名称" min-width="200" show-overflow-tooltip />
      <el-table-column prop="standardTypeName" label="标准类型" min-width="100" show-overflow-tooltip />
      <el-table-column label="标准状态" min-width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="publishDate" label="发布日期" min-width="180" show-overflow-tooltip />
      <el-table-column prop="executeDate" label="实施日期" min-width="180" show-overflow-tooltip />
      <el-table-column label="操作" min-width="100"  fixed="right" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('cancel', row)" class="c-primary pointer">取消收藏</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
  import { upadteCollect } from '@/api/collect';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    bean: {
      type: Object,
      default: () => {
        return {};
      },
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    total: {
      type: [String, Number],
      default: 0,
    },
    queryParams: {
      type: Object,
    },
  });

  const { loading, bean, tableData, queryParams } = toRefs(props);

  const getStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-045CFE';
      case 1:
        return 'status-04AE00';
      case 2:
        return '';
      case 3:
        return 'status-999999';
      case 4:
        return 'status-FF0000';
      default:
        return '';
    }
  };

  const handleClick = (type, data) => {
    switch (type) {
      case 'jump':
        window.open(data, '_blank');
        break;
      case 'cancel':
        proxy
          .$confirm('确认取消收藏当前数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            upadteCollect({ id: data.id, recordId: data.recordId, recordType: 1, isCollect: 0 }).then(res => {
              proxy.$modal.msgSuccess('取消收藏成功！');
              emit('updateData');
            });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  const emit = defineEmits(['updateData']);
</script>

<style lang="scss" scoped></style>
