<template>
  <div class="standard-chat">
    <div class="chat-title">行业分类统计</div>
    <div class="chat-search">
      <chat-search-form v-model:queryParams="queryParams" type="oneLine" @change="getData" />
    </div>
    <div class="chat-content">
      <stat-bar-chart v-if="chartDataBar && chartDataBar.dmList && chartDataBar.dmList.length > 0" :chart-data="chartDataBar" :height="'380px'" />
      <bxc-empty v-else />
    </div>
  </div>
</template>
<script setup>
import ChatSearchForm from '@/views/components/standard-stat/data-stat/ChatSearchForm'
import StatBarChart from '@/views/components/standard-stat/data-stat/StatBarChart'
import { getIndustryStat } from '@/api/standard-stat'

const queryParams = ref({
  publishStartDate: undefined,
  publishEndDate: undefined,
  standardType: '1', // 0: 国家标准, 1: 行业标准, 2: 地方标准, 3: 团体标准, 4: 企业标准, 8: 规范性文件, 9: 计量技术规范
  standardStatusList: ['1']
})
const chartDataBar = ref([])
const loading = ref(false)

const getData = () => {
  loading.value = true;
  getIndustryStat(queryParams.value).then((response) => {
    if(response.data){
      chartDataBar.value = response.data
    }
    loading.value = false
  }).catch(() => {
    loading.value = false
  });
}

getData()
</script>