<template>
  <div class="workbench-list">
    <div v-for="item in dataList" :key="item.key" class="workbench-item">
      <div class="item-title">{{item.name}}</div>
      <div class="item-num">{{item.num}}</div>
      <div class="item-icon">
        <img :src="item.img" alt="">
      </div>
    </div>
  </div>
</template>
<script setup>
import { getOverviewStat } from "@/api/workbench"
import OVERVIEW0 from "@/assets/images/workbench/overview-0.png"
import OVERVIEW1 from "@/assets/images/workbench/overview-1.png"
import OVERVIEW2 from "@/assets/images/workbench/overview-2.png"
import OVERVIEW3 from "@/assets/images/workbench/overview-3.png"
import OVERVIEW4 from "@/assets/images/workbench/overview-4.png"
import OVERVIEW5 from "@/assets/images/workbench/overview-5.png"

const dataList = ref([
  {
    name: "托管标准",
    img: OVERVIEW0,
    key: "trusteeshipCount",
    num: "0"
  },{
    name: "我的体系",
    img: OVERVIEW1,
    key: "systemCount",
    num: "0"
  },{
    name: "我的订阅",
    img: OVERVIEW2,
    key: "subscriptionCount",
    num: "0"
  },{
    name: "我的收藏",
    img: OVERVIEW3,
    key: "collectionCount",
    num: "0"
  },{
    name: "我的编写",
    img: OVERVIEW4,
    key: "writeCount",
    num: "0"
  },{
    name: "我的服务",
    img: OVERVIEW5,
    key: "serviceCount",
    num: "0"
  },
  
])

const getData = () => {
  getOverviewStat().then(res => {
    if(res.data){
      dataList.value.forEach(item => {
        if(res.data[item.key]){
          item.num = res.data[item.key] || 0
        }
      })
    }
  })
}

getData()

</script>
<style lang="scss" scoped>
.workbench-list{
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  .workbench-item{
    flex: calc((100% - 140px) / 5);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: linear-gradient(151deg, #DFEAFE 0%, #F5F9FF 100%);
    border-radius: 5px;
    border: 1px solid #E9F0FF;
    height: 160px;
    box-sizing:border-box;
    padding: 0px 5px 0px 20px;
    .item-title{
      margin-top: 18px;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
    .item-num{
      margin-top: 2px;
      font-weight: bold;
      font-size: 34px;
      color: $primary-color;
    }
    .item-icon{
      height: 70px;
      align-self: flex-end;
      overflow: hidden;

      img{
        height: 70px;
      }
    }
  }
}
</style>