<template>
  <div>
    <div v-if="props.total > 0" class="flex flex-sb mb15">
      <div class="overview-wrap">
        <div>
          收藏TC委员会：
          <span class="num">{{ props.total }}</span>
        </div>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData" :border="false">
      <template v-slot:empty>
        <bxc-empty class="mt30" />
      </template>
      <el-table-column label="序号" fixed width="90">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="委员会编号" min-width="200" fixed="left" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('jump', '/retrieval/tcDetail?id=' + row.recordId)" class="c-primary pointer">
            {{ row.committeeNumber }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="cnCommitteeName" label="标准委员会名称名称" min-width="200" show-overflow-tooltip />
      <el-table-column prop="currentSecretaryGeneral" label="秘书长" min-width="180" show-overflow-tooltip />
      <el-table-column prop="sessionNumber" label="届号" min-width="100" show-overflow-tooltip />
      <el-table-column prop="secretariatUnit" label="秘书处所在单位" min-width="180" show-overflow-tooltip />
      <el-table-column prop="responsibleProfessionalScope" label="专业范围" min-width="180" show-overflow-tooltip />
      <el-table-column label="操作" min-width="100" fixed="right" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('cancel', row)" class="c-primary pointer">取消收藏</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
  import { upadteCollect } from '@/api/collect';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    bean: {
      type: Object,
      default: () => {
        return {};
      },
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    total: {
      type: [String, Number],
      default: 0,
    },
    queryParams: {
      type: Object,
    },
  });

  const { loading, bean, tableData, queryParams } = toRefs(props);

  const handleClick = (type, data) => {
    switch (type) {
      case 'jump':
        window.open(data, '_blank');
        break;
      case 'cancel':
        proxy
          .$confirm('确认取消收藏当前数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            upadteCollect({ id: data.id, recordId: data.recordId, recordType: 2, isCollect: 0 }).then(res => {
              proxy.$modal.msgSuccess('取消收藏成功！');
              emit('updateData');
            });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  const emit = defineEmits(['updateData']);
</script>

<style lang="scss" scoped></style>
