/**
 * 通用css样式布局处理
 * Copyright (c) 2019 ruoyi
 */

/** 基础通用 **/
.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.flex-sb {
  justify-content: space-between;
}
.flex-sa {
  justify-content: space-around;
}
.flex-wrap {
  flex: wrap;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-ai-center {
  align-items: center;
}
.flex-ai-start {
  align-items: flex-start;
}
.flex-ac-start {
  align-content: flex-start;
}
.flex-jc-center {
  justify-content: center;
}
.flex-jc-start {
  justify-content: flex-start;
}
.flex-jc-end {
  justify-content: flex-end;
}
.flex-1 {
  flex: 1;
}
.flex-shrink {
  flex-shrink: 0;
}
.ta-l {
  text-align: left;
}
.ta-c {
  text-align: center;
}
.ta-r {
  text-align: right;
}
.c-primary {
  color: $primary-color !important;
}
.c-33 {
  color: #333333;
}
.c-66 {
  color: #666666;
}
.c-00 {
  color: #000000;
}
.c-99 {
  color: #999999;
}
.c-3E {
  color: #333333;
}
.c-ff {
  color: #ffffff;
}
.c-E5 {
  color: #e5e8ef;
}
.c-8F {
  color: #8f9bb4;
}
.c-FFC600 {
  color: #ffc600;
}
.c-F20000 {
  color: #f20000 !important;
}
.c-FF0000 {
  color: #ff0000 !important;
}
.c-F28900 {
  color: #f28900 !important;
}
.c-09AA01 {
  color: #09aa01 !important;
}
.c-EE0000 {
  color: #ee0000 !important;
}
.c-8F9BB3 {
  color: #8f9bb3 !important;
}
.bgc-primary {
  background-color: $primary-color !important;
}
.c-FFB400 {
  color: #ffb400 !important;
}
.c-running {
  color: #2F5AFF;
}
.c-terminated {
  color: #D20D0D;
}
.c-completed {
  color: #00B526;
}
.c-canceled {
  color: #B8B8B8;
}
.bgc-ff {
  background-color: #ffffff;
}
.bgc-99 {
  background-color: #999999;
}
.bgc-EE0000 {
  background-color: #ee0000;
}
.bgc-09AA01 {
  background-color: #09aa01;
}
.bgc-8F9BB3 {
  background-color: #8f9bb3;
}
.bgc-F8F9FB {
  background-color: #F8F9FB;
}
.bgc-running {
  background-color: #2F5AFF;
}
.bgc-terminated {
  background-color: #D20D0D;
}
.bgc-completed {
  background-color: #00B526;
}
.bgc-canceled {
  background-color: #B8B8B8;
}
.f-10 {
  font-size: 10px !important;
}
.f-12 {
  font-size: 12px !important;
}
.f-14 {
  font-size: 14px !important;
}
.f-16 {
  font-size: 16px !important;
}
.f-17 {
  font-size: 17px !important;
}
.f-18 {
  font-size: 18px !important;
}
.f-19 {
  font-size: 19px !important;
}
.f-20 {
  font-size: 20px !important;
}
.f-22 {
  font-size: 22px !important;
}
.f-24 {
  font-size: 24px !important;
}
.f-32 {
  font-size: 32px !important;
}
.f-40 {
  font-size: 40px !important;
}
.f-bold {
  font-weight: bold;
}
.fw-b {
  font-weight: bold;
}
.pl10 {
  padding-left: 10px;
}
.pl20 {
  padding-left: 20px;
}
.pl30 {
  padding-left: 30px;
}
.p20 {
  padding: 20px;
}
.p30 {
  padding: 30px;
}
.pt5 {
  padding-top: 5px;
}
.pr5 {
  padding-right: 5px;
}
.pr30 {
  padding-right: 30px;
}
.pb5 {
  padding-bottom: 5px;
}
.mt5 {
  margin-top: 5px;
}
.mt10 {
  margin-top: 10px;
}
.mt15 {
  margin-top: 15px;
}
.mt20 {
  margin-top: 20px;
}
.mt25 {
  margin-top: 25px;
}
.mt30 {
  margin-top: 30px;
}
.mt40 {
  margin-top: 40px;
}
.mt50 {
  margin-top: 50px;
}
.mr5 {
  margin-right: 5px;
}
.mr6 {
  margin-right: 6px;
}
.mr10 {
  margin-right: 10px;
}
.mr15 {
  margin-right: 15px;
}
.mr20 {
  margin-right: 20px;
}
.mr25 {
  margin-right: 25px !important;
}
.mr50 {
  margin-right: 50px;
}
.mr80 {
  margin-right: 80px;
}
.mb5 {
  margin-bottom: 5px;
}
.mb8 {
  margin-bottom: 8px;
}
.mb10 {
  margin-bottom: 10px;
}
.mb15 {
  margin-bottom: 15px;
}
.mb20 {
  margin-bottom: 20px;
}
.ml5 {
  margin-left: 5px;
}
.ml10 {
  margin-left: 10px;
}
.ml12 {
  margin-left: 12px !important;
}
.ml15 {
  margin-left: 15px;
}
.ml20 {
  margin-left: 20px;
}
.ml30 {
  margin-left: 30px;
}
.ml50 {
  margin-left: 50px;
}
.ml80 {
  margin-left: 80px;
}
.br-15 {
  border-radius: 15px;
}
.h50 {
  height: 50px !important;
}
.lh20{
  line-height: 20px !important;
}
// 标签
.h-title {
  font-size: 18px;
  color: #3c4965;
  position: relative;
  padding-left: 10px;
  box-sizing: border-box;
  font-weight: bold;

  &::before {
    content: '';
    position: absolute;
    top: calc(50% - 10px);
    left: 0px;
    width: 4px;
    height: 20px;
    background: #2f5aff;
    border-radius: 2px;
  }
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}

// dialog弹窗
.el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.el-dialog__body {
  padding: 20px 20px 10px !important;
  box-sizing: border-box !important;
  max-height: calc(100vh - 150px) !important;
  max-width: 100% !important;
  overflow-y: scroll !important;
}

.el-dialog__body::-webkit-scrollbar {
  width: 7px !important;
  height: 10px !important;
}

.el-dialog__body::-webkit-scrollbar-thumb {
  background-color: #dedfe0 !important;
  border-radius: 10px !important;
}

.el-dialog__header {
  margin-right: 0 !important;
  border-bottom: 1px solid #e5e8ef;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 100px !important;
}

.el-dialog__title {
  font-size: 20px !important;
  font-weight: bold;
}

.el-dialog__close {
  color: #8f9bb3 !important;
  font-size: 30px !important;
}

.el-dialog:not(.is-fullscreen) {
  border-radius: 5px;
}

// 抽屉
.el-drawer__header {
  height: 50px !important;
  line-height: 50px !important;
  padding: 0 20px !important;
  box-sizing: border-box;
  margin-bottom: 15px !important;
  border-bottom: 1px solid #e5e8ef !important;
  .el-drawer__title,
  h4 {
    font-size: 20px !important;
    color: #333333 !important;
    font-weight: bold;
  }
  .el-drawer__close-btn {
    padding: 0 !important;
  }
}

.el-drawer__body {
  padding: 0px 13px 40px 20px !important;
  box-sizing: border-box;
  max-height: calc(100vh - 75px) !important;
  max-width: 100% !important;
  overflow-y: scroll !important;
}

.el-drawer__body::-webkit-scrollbar {
  width: 7px !important;
  height: 10px !important;
}

.el-drawer__body::-webkit-scrollbar-thumb {
  background-color: #dedfe0 !important;
  border-radius: 10px !important;
}

// table
.el-table {
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      word-break: break-word;
      background-color: #f8f8f9 !important;
      color: #515a6e;
      height: 40px !important;
      font-size: 13px;
    }
  }
  .el-table__body-wrapper {
    .el-button [class*='el-icon-'] + span {
      margin-left: 1px;
    }
  }
}

// 描述列表
.el-descriptions__label {
  // background-color: #f3f6fd !important;
}
.el-descriptions__cell {
  color: #333333 !important;
}

/** 表单布局 **/
.form-header {
  font-size: 15px;
  color: #6379bb;
  border-bottom: 1px solid #ddd;
  margin: 8px 10px 25px 10px;
  padding-bottom: 5px;
}

.el-form-item__label {
  font-size: 16px !important;
}

.el-form-item--default {
  // margin-bottom: 10px !important;
}

/* tree border */
.tree-border {
  margin-top: 5px;
  border: 1px solid #e5e6e7;
  background: #ffffff none;
  border-radius: 4px;
  width: 100%;
}

@media (max-width: 768px) {
  .pagination-container .el-pagination > .el-pagination__jump {
    display: none !important;
  }
  .pagination-container .el-pagination > .el-pagination__sizes {
    display: none !important;
  }
}

.el-table .fixed-width .el-button--small {
  padding-left: 0;
  padding-right: 0;
  width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
  cursor: pointer;
  color: #409eff;
  margin-left: 10px;
}

.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 12px;
}

.el-tree-node__content > .el-checkbox {
  margin-right: 8px;
}

.list-group-striped > .list-group-item {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
}

.list-group {
  padding-left: 0px;
  list-style: none;
}

.list-group-item {
  border-bottom: 1px solid #e7eaec;
  border-top: 1px solid #e7eaec;
  margin-bottom: -1px;
  padding: 11px 0px;
  font-size: 13px;
}

.pull-right {
  float: right !important;
}

.el-card__header {
  padding: 14px 15px 7px !important;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px !important;
}

.card-box {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  background: #20b2aa;
  border-color: #20b2aa;
  color: #ffffff;
}

.el-button--cyan:focus,
.el-button--cyan:hover {
  background: #48d1cc;
  border-color: #48d1cc;
  color: #ffffff;
}

.el-button--cyan {
  background-color: #20b2aa;
  border-color: #20b2aa;
  color: #ffffff;
}

/* text color */
.text-navy {
  color: #1ab394;
}

.text-primary {
  color: inherit;
}

.text-success {
  color: #1c84c6;
}

.text-info {
  color: #23c6c8;
}

.text-warning {
  color: #f8ac59;
}

.text-danger {
  color: #ed5565;
}

.text-muted {
  color: #888888;
}

/* image */
.img-circle {
  border-radius: 50%;
}

.img-lg {
  width: 120px;
  height: 120px;
}

.avatar-upload-preview {
  position: absolute;
  top: 50%;
  transform: translate(50%, -50%);
  width: 200px;
  height: 200px;
  border-radius: 50%;
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
}

/* 拖拽列样式 */
.sortable-ghost {
  opacity: 0.8;
  color: #fff !important;
  background: #42b983 !important;
}

/* 表格右侧工具栏样式 */
.top-right-btn {
  margin-left: auto;
}

.pointer {
  cursor: pointer;
}
.m-red {
  color: #FF0000;
}
.m-blue {
  color: $primary-color;
}
.m-green {
  color: #04AE00;
}
.m-gray {
  color: #888888;
}
.m-yellow {
  color: #FFA200;
}