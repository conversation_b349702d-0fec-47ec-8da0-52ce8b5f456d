import request from '@/utils/request'

// 查询服务反馈信息列表
export function getFeedbackList(params) {
  return request({
    url: '/business/serveFeedbackInfo/list',
    method: 'get',
    params,
  });
}
// 获取服务反馈信息详细信息
export function getFeedbackDetail(id) {
  return request({
    url: '/business/serveFeedbackInfo/'+ id,
    method: 'get'
  });
}
// 新增服务反馈信息
export function addFeedback(data) {
  return request({
    url: '/business/serveFeedbackInfo',
    method: 'post',
    data,
  });
}
// 撤回服务反馈信息
export function revokeFeedback(id) {
  return request({
    url: '/business/serveFeedbackInfo/withdraw/'+ id,
    method: 'put'
  });
}
// 删除服务反馈信息
export function deleteFeedback(data) {
  return request({
    url: '/business/serveFeedbackInfo/remove',
    method: 'delete',
    data,
  });
}