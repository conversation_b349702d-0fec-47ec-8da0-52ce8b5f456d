<template>
  <div class="standard-chat">
    <div class="chat-title">标准CCS分类统计</div>
    <div class="chat-search">
      <chat-search-form v-model:queryParams="queryParams" type="oneLine" @change="getData" />
    </div>
    <div class="chat-content">
      <stat-horizontal-bar-chart v-if="chartDataBar && chartDataBar.dmList && chartDataBar.dmList.length > 0" :chart-data="chartDataBar" :height="'600px'" />
      <bxc-empty v-else />
    </div>
  </div>
</template>
<script setup>
import ChatSearchForm from '@/views/components/standard-stat/data-stat/ChatSearchForm'
import StatHorizontalBarChart from '@/views/components/standard-stat/data-stat/StatHorizontalBarChart'
import { getCCSCategory } from '@/api/standard-stat'

const props = defineProps(['standardType'])

const queryParams = ref({
  publishStartDate: undefined,
  publishEndDate: undefined,
  standardType: props.standardType, // 0: 国家标准, 1: 行业标准, 2: 地方标准, 3: 团体标准, 4: 企业标准, 8: 规范性文件, 9: 计量技术规范
  standardStatusList: ['1']
})
const chartDataBar = ref([])
const loading = ref(false)

const getData = () => {
  loading.value = true;
  getCCSCategory(queryParams.value).then((response) => {
    if(response.data){
      chartDataBar.value = response.data
    }
    loading.value = false
  }).catch(() => {
    loading.value = false
  });
}

getData()
</script>