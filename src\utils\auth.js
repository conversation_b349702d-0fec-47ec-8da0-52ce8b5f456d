import Cookies from 'js-cookie'

const TokenKey = 'token'
const UserInfoKey = 'userInfo'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
export function getUserInfo() {
  if(Cookies.get(UserInfoKey)){
    return JSON.parse(Cookies.get(UserInfoKey))
  }else{
    return {}
  }
}

export function setUserInfo(userInfo) {
  return Cookies.set(UserInfoKey, JSON.stringify(userInfo))
}

export function removeUserInfo() {
  return Cookies.remove(UserInfoKey)
}

