<template>
  <div class="pop-container">
    <el-dialog
      :modal-append-to-body="false"
      v-model="props.open"
      width="60%"
      title="添加体系标准"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClose"
    >
      <div class="standard-import-list">
        <div class="f-22 f-bold c-33 mb10">节点名称: {{ props.name }}</div>
        <!-- 条件区 -->
        <div class="flex">
          <div class="flex-1 flex">
            <div class="search">
              <el-autocomplete
                style="width: 100%"
                v-model="searchStr"
                value-key="standardName"
                :fetch-suggestions="
                  (queryString, cb) => {
                    querySearchAsync(queryString, cb, index);
                  }
                "
                placeholder="搜索/选择需要加入体系的标准"
                @select="handleSelect"
                clearable
              >
                <template #default="{ item }">
                  <div class="flex">
                    <div>{{ item.standardCode }}</div>
                    <div class="ml10" style="margin-right: auto">{{ item.standardName }}</div>
                    <div class="ml10 f-18 c-primary flex flex-ai-center flex-shrink">
                      <el-icon><CirclePlusFilled /></el-icon>
                    </div>
                  </div>
                </template>
              </el-autocomplete>
            </div>
          </div>
        </div>
        <el-table v-loading="loading" ref="tableRef" :data="dataList" class="mt15 mb20">
          <el-table-column type="index" min-width="55" fixed="left" />
          <el-table-column prop="standardCode" label="标准号" min-width="200" show-overflow-tooltip fixed="left" />
          <el-table-column prop="standardName" label="标准名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="standardTypeName" label="标准类型" min-width="150" show-overflow-tooltip />
          <el-table-column label="标准状态" show-overflow-tooltip>
            <template #default="{ row }">
              <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right">
            <template #default="scope">
              <el-button type="danger" icon="Delete" link @click="handleItemDelete(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button @click.prevent="handleSubmt" type="primary" :loading="loading">
            <span v-if="!loading">提交</span>
            <span v-else>提交中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getStandardList, importStandardSystemItem } from '@/api/standard-system/my';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    open: Boolean,
    nodeId: [String, Number],
    name: String,
  });

  const loading = ref(false);
  const dataList = ref([]);
  const searchStr = ref('');

  const getStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-045CFE';
      case 1:
        return 'status-04AE00';
      case 2:
        return '';
      case 3:
        return 'status-999999';
      case 4:
        return 'status-FF0000';
      default:
        return '';
    }
  };

  const handleItemDelete = index => {
    dataList.value.splice(index, 1);
  };
  const querySearchAsync = (queryString, cb, index) => {
    let list = dataList.value.map(item => item.id);
    let params = {
      keyword: queryString,
      searchStandardIdList: list,
      pageNum: 1,
      pageSize: 10,
    };
    getStandardList(params)
      .then(response => {
        if (response.rows) {
          cb(response.rows);
        }
      })
      .catch(() => {
        cb();
      });
  };
  const handleSelect = item => {
    dataList.value.push(item);
    searchStr.value = undefined;
    document.activeElement.blur();
  };

  const handleSubmt = () => {
    //判断条件
    if (!isValid()) {
      return;
    }
    loading.value = true;

    // 参数
    let ids = dataList.value.map(d => d.id);
    let params = {
      standardIds: ids,
      nodeId: props.nodeId,
    };

    importStandardSystemItem(params)
      .then(response => {
        proxy.$modal.msgSuccess('体系标准添加成功！');
        emit('success');
        handleClose();
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const isValid = () => {
    if (dataList.value.length == 0) {
      proxy.$modal.msgError('请选择体系标准');
      return false;
    }
    return true;
  };

  const handleClose = () => {
    emit('update:open', false);
  };

  const emit = defineEmits(['update:open', 'success']);
</script>

<style lang="scss" scoped>
  .search {
    width: 40%;
  }
</style>
