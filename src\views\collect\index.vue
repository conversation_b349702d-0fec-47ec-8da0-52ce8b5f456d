<template>
  <div class="app-container">
    <div class="bgc-ff">
      <div class="app-tabs">
        <div class="flex">
          <div
            @click="handleTabs(index)"
            v-for="(item, index) in tabsList"
            :key="index"
            class="app-tabs-item"
            :class="{ 'app-tabs-active': index == activeTab }"
          >
            {{ item.title + '(' + (bean[item.fieldName] || 0) + ')' }}
          </div>
        </div>
      </div>
      <div class="app-container-search">
        <el-form :model="queryParams" ref="formRef" label-width="auto" :inline="true">
          <template v-if="activeTab == 0 || activeTab == 1">
            <el-form-item label="标准号" prop="standardCode">
              <el-input v-model="queryParams.standardCode" placeholder="请输入标准号" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="标准名称" prop="standardName">
              <el-input v-model="queryParams.standardName" placeholder="请输入标准名称检索" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="标准状态" prop="standardStatus">
              <el-select v-model="queryParams.standardStatus" placeholder="请选择标准状态" clearable>
                <el-option v-for="dict in bxc_standard_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="activeTab == 0">
            <el-form-item label="标准类型" prop="standardType">
              <el-select v-model="queryParams.standardType" placeholder="请选择标准类型" clearable>
                <el-option
                  v-show="dict.value != 5 && dict.value != 6"
                  v-for="dict in bxc_standard_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="activeTab == 1">
            <el-form-item label="标准类型" prop="standardType">
              <el-select v-model="queryParams.standardType" placeholder="请选择标准类型" clearable>
                <el-option
                  v-show="dict.value == 5 || dict.value == 6"
                  v-for="dict in bxc_standard_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="activeTab == 2">
            <el-form-item label="委员会编号" prop="committeeNumber">
              <el-input v-model="queryParams.committeeNumber" placeholder="请输入委员会编号" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="委员会名称" prop="cnCommitteeName">
              <el-input v-model="queryParams.cnCommitteeName" placeholder="请输入委员会名称" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="所在单位" prop="secretariatUnit">
              <el-input
                v-model="queryParams.secretariatUnit"
                placeholder="请输入委员会秘书处所在单位"
                @keyup.enter="getData('pageNum')"
              />
            </el-form-item>
          </template>
          <template v-if="activeTab == 3">
            <el-form-item label="样品编号" prop="sampleCode">
              <el-input v-model="queryParams.sampleCode" placeholder="请输入样品编号" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="样品名称" prop="sampleName">
              <el-input v-model="queryParams.sampleName" placeholder="请输入样品名称" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="状态" prop="sampleStatus">
              <el-select v-model="queryParams.sampleStatus" placeholder="请选择样品状态" clearable>
                <el-option v-for="dict in bxc_std_sample_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="归口单位" prop="registryUnit">
              <el-input v-model="queryParams.registryUnit" placeholder="请输入归口单位" @keyup.enter="getData('pageNum')" />
            </el-form-item>
          </template>
          <template v-if="activeTab == 4">
            <el-form-item label="专家名称" prop="commissionerName">
              <el-input v-model="queryParams.commissionerName" placeholder="请输入专家名称" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="工作单位" prop="workUnit">
              <el-input v-model="queryParams.workUnit" placeholder="请输入工作单位" @keyup.enter="getData('pageNum')" />
            </el-form-item>
          </template>
          <template v-if="activeTab == 5">
            <el-form-item label="公告标题" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入公告标题" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="公告号" prop="noticeCode">
              <el-input v-model="queryParams.noticeCode" placeholder="请输入公告号" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="公告类型" prop="noticeType">
              <el-select v-model="queryParams.noticeType" placeholder="请选择公告类型" clearable>
                <el-option v-for="dict in bxc_std_notice_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="发布单位" prop="publishingUnit">
              <el-input v-model="queryParams.publishingUnit" placeholder="请输入发布单位" @keyup.enter="getData('pageNum')" />
            </el-form-item>
          </template>
          <template v-if="activeTab == 6">
            <el-form-item label="标题" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入标题" @keyup.enter="getData('pageNum')" />
            </el-form-item>
          </template>
          <template v-if="activeTab == 7">
            <el-form-item label="计划号" prop="planNumber">
              <el-input v-model="queryParams.planNumber" placeholder="请输入计划号" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="项目名称" prop="entryName">
              <el-input v-model="queryParams.entryName" placeholder="请输入项目名称" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="计划类型" prop="type">
              <el-select v-model="queryParams.type" placeholder="请选择计划类型" clearable>
                <el-option v-for="dict in bxc_std_plan_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="制修订" prop="amend">
              <el-select v-model="queryParams.amend" placeholder="请选择制修订" clearable>
                <el-option v-for="dict in bxc_standard_amend" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="activeTab == 8">
            <el-form-item label="知识名称" prop="knowledgeName">
              <el-input v-model="queryParams.knowledgeName" placeholder="请输入知识名称" @keyup.enter="getData('pageNum')" />
            </el-form-item>
            <el-form-item label="所属产业" prop="economicTypeCodes">
              <el-cascader
                v-model="queryParams.economicTypeCodes"
                :options="industryOptions"
                :props="{ label: 'name', value: 'code', emitPath: false, checkStrictly: true }"
                clearable
                placeholder="请选择所属产业"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="收藏日期" prop="collectionDate">
              <el-date-picker
                v-model="collectionDate"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="关联标准" prop="standardCodes">
              <el-input v-model="queryParams.standardCodes" placeholder="请输入标准号" @keyup.enter="getData('pageNum')" />
            </el-form-item>
          </template>
          <el-form-item label="">
            <el-button type="primary" icon="Search" @click="getData('pageNum')">查询</el-button>
            <el-button plain icon="Refresh" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="app-container-content mt15">
      <component
        :is="getActiveTabComponent"
        :loading="loading"
        :bean="bean"
        :tableData="tableData"
        :total="total"
        :queryParams="queryParams"
        @updateData="getData"
      />
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
  </div>
</template>
<script setup>
  import { getCollectList, getNationalEconomicTypeTree } from '@/api/collect';
  import Domestic from '@/views/components/collect/Domestic.vue';
  import internation from '@/views/components/collect/internation.vue';
  import Tc from '@/views/components/collect/Tc.vue';
  import Sample from '@/views/components/collect/Sample.vue';
  import Expert from '@/views/components/collect/Expert.vue';
  import Announcement from '@/views/components/collect/Announcement.vue';
  import Law from '@/views/components/collect/Law.vue';
  import Plan from '@/views/components/collect/Plan.vue';
  import Knowledge from '@/views/components/collect/Knowledge.vue';

  const { proxy } = getCurrentInstance();
  const {
    bxc_standard_type,
    bxc_standard_status,
    bxc_std_sample_status,
    bxc_std_notice_type,
    bxc_std_plan_type,
    bxc_standard_amend,
  } = proxy.useDict(
    'bxc_standard_type',
    'bxc_standard_status',
    'bxc_std_sample_status',
    'bxc_std_notice_type',
    'bxc_std_plan_type',
    'bxc_standard_amend'
  );

  const loading = ref(false);
  const activeTab = ref(0);
  const tabsList = reactive([
    { title: '标准', fieldName: 'recordType0' },
    { title: '国际国外标准', fieldName: 'recordType1' },
    { title: 'TC目录', fieldName: 'recordType2' },
    { title: '标准样品', fieldName: 'recordType3' },
    { title: '标准专家', fieldName: 'recordType4' },
    { title: '标准公告', fieldName: 'recordType5' },
    { title: '法律法规', fieldName: 'recordType6' },
    { title: '计划标准', fieldName: 'recordType7' },
    { title: '标准知识', fieldName: 'recordType8' },
  ]);
  const industryOptions = ref([]);
  const collectionDate = ref([]);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    recordType: 0,
  });
  const bean = ref({});
  const tableData = ref([]);
  const total = ref(0);

  const getActiveTabComponent = computed(() => {
    switch (Number(activeTab.value)) {
      case 0:
        return Domestic;
      case 1:
        return internation;
      case 2:
        return Tc;
      case 3:
        return Sample;
      case 4:
        return Expert;
      case 5:
        return Announcement;
      case 6:
        return Law;
      case 7:
        return Plan;
      case 8:
        return Knowledge;
      default:
        return;
    }
  });

  const getSelect = () => {
    getNationalEconomicTypeTree({ deep: 2 }).then(res => {
      industryOptions.value = res.data;
    });
  };

  const getData = data => {
    loading.value = true;
    if (data) queryParams.value.pageNum = 1;
    queryParams.value.startTime = collectionDate.value && collectionDate.value.length > 0 ? collectionDate.value[0] : '';
    queryParams.value.endTime = collectionDate.value && collectionDate.value.length > 0 ? collectionDate.value[1] : '';
    queryParams.value.recordType = activeTab.value;
    getCollectList(queryParams.value)
      .then(res => {
        tableData.value = res.rows || [];
        total.value = res.total || 0;
        bean.value = res.bean || {};
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleReset = () => {
    proxy.$refs.formRef.resetFields();
    collectionDate.value = [];
    getData('pageNum');
  };

  const handleTabs = index => {
    if (index == activeTab.value) return;
    activeTab.value = index;
    handleReset();
  };

  getSelect();
  getData();
</script>

<style lang="scss" scoped>
  .app-tabs {
    padding: 5px 30px 0 10px;
    box-sizing: border-box;
  }

  :deep(.el-button:focus) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:hover) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:active) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }
</style>
