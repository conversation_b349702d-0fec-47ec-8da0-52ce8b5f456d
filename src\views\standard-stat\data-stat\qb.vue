<template>
  <div class="app-container">
    <el-tabs v-model="activeName" class="stat-tabs" @tab-click="handleClick">
      <el-tab-pane label="基础属性统计" name="base">
      </el-tab-pane>
    </el-tabs>

    <div class="stat-content">
      <template v-if="activeName == 'base'">
        <div class="stat-two-column">
          <standard-status-stat standardType="4" class="half-column" />
        </div>
      </template>
    </div>
  </div>
  </template>
  <script setup>
  import StandardStatusStat from '@/views/components/standard-stat/data-stat/StandardStatusStat.vue'

  const activeName = ref('base')
  
  const handleClick = (tab) => {
    
  }
  </script>
  <style lang="scss" scoped>

  </style>