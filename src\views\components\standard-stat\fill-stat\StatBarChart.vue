<template>
  <div v-resize="onResize" :class="className" :style="{ height: height, width: width }" />
</template>

<script setup>
  import * as echarts from 'echarts';
  // import resize from '@/views/dashboard/mixins/resize'

  const { proxy } = getCurrentInstance();
  // mixins: [resize],
  const props = defineProps({
    className: {
      type: String,
      default: 'chart',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '350px',
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    chartData: {
      type: Object,
      required: true,
    },
  });
  const { className, width, height, autoResize, chartData } = toRefs(props);

  const data = reactive({
    chart: null,
  });

  const { chart } = toRefs(data);

  watch(
    () => props.chartData,
    newVal => {
      setOptions(newVal);
    },
    { deep: true }
  );

  onMounted(() => {
    proxy.$nextTick(() => {
      initChart();
    });
  });
  onBeforeUnmount(() => {
    if (!data.chart) {
      return;
    }
    data.chart.dispose();
    data.chart = null;
  });

  const initChart = () => {
    data.chart = markRaw(echarts.init(proxy.$el));
    setOptions(props.chartData);
  };
  const colors = ['#2F5AFF', '#67BBDC', '#F69D0D', '#EFEF61', '#D537ED', '#641BCE', '#0336FF'];

  let series = [];

  const setSeries = () => {
    series = [];
    for (let i = 0; i < props.chartData.nodeData.length; i++) {
      series.push({
        name: props.chartData.nodeData[i].name,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#045CFF', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#00AEFF', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        type: 'bar',
        // barWidth: '15',
        data: props.chartData.nodeData[i].echartsData,
        animationDuration: 2800,
        animationEasing: 'cubicInOut',
      });
    }
  };

  const setOptions = ({ dmList, nodeData } = {}) => {
    if (!dmList) return;
    setSeries();
    data.chart.setOption({
      xAxis: {
        data: dmList,
        boundaryGap: true,
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#E8E8E8',
          },
        },
        axisLabel: {
          formatter: function (data) {
            let valueTxt = '';
            if (data.length > 7) {
              valueTxt = data.substring(0, 7) + '...';
            } else {
              valueTxt = data;
            }
            return valueTxt;
          },
          textStyle: {
            fontSize: '12',
            color: '#666666', // 坐标值的具体的颜色
          },
          interval: 0,
          // 设置字体的倾斜角度
          rotate: 0,
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            color: 'rgb(0, 176, 255)',
          },
        },
      },
      grid: {
        left: 30,
        right: 50,
        bottom: 20,
        top: 30,
        containLabel: true,
      },
      yAxis: {
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: ['#E8E8E8'],
          },
        },
        axisLabel: {
          color: '#666666',
        },
      },
      dataZoom: [
        {
          show: dmList.length > 35,
          type: 'slider',
          startValue: dmList.length - 35,
          endValue: dmList.length,
        },
        {
          show: dmList.length > 35,
          start: dmList.length - 35,
          end: dmList.length,
        },
      ],
      series: series,
    });
  };
  const onResize = () => {
    data.chart && data.chart.resize();
  };
</script>
