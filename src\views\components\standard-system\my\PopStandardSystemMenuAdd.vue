<template>
  <div class="pop-container">
    <el-dialog
      :modal-append-to-body="false"
      v-model="props.open"
      width="400px"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClose"
    >
      <el-form :model="form" label-position="top" ref="formRef" :rules="formRules" @submit.native.prevent>
        <el-form-item v-if="props.opType == 0" label="父级节点" prop="pid">
          <el-tree-select
            style="width: 100%"
            v-model="form.pid"
            :data="categoryList"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            placeholder="请选择父级节点"
            :default-expand-all="true"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="节点名称" prop="name">
          <el-input maxlength="30" v-model="form.name" placeholder="请输入节点名称" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button @click.prevent="handleSubmit" type="primary" :loading="loading">
            <span v-if="!loading">提交</span>
            <span v-else>提交中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getStandardSystemItemDetail, addStandardSystemTree, updateStandardSystemTree } from '@/api/standard-system/my';
  import { reactive } from 'vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    open: Boolean,
    menuList: {
      type: Array,
      default: [],
    },
    id: [String, Number],
    systemId: [String, Number],
    opType: {
      type: Number,
      default: 0, // 0:新增,1:重命名
    },
  });

  const categoryList = computed(() => {
    return [{ id: 0, name: '主目录', children: props.menuList }];
  });

  const title = ref('添加节点');
  const loading = ref(false);
  const form = ref({
    pid: undefined,
    id: undefined,
    name: '',
    systemId: props.systemId,
  });
  const formRules = reactive({
    name: [{ required: true, trigger: 'blur', message: '节点名称不能为空' }],
  });

  const getData = () => {
    loading.value = true;
    getStandardSystemItemDetail(props.id)
      .then(response => {
        form.value = response.data;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleSubmit = () => {
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        loading.value = true;
        if (form.value.id) {
          updateStandardSystemTree(form.value)
            .then(response => {
              proxy.$modal.msgSuccess('重命名成功！');
              emit('success');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        } else {
          addStandardSystemTree(form.value)
            .then(response => {
              proxy.$modal.msgSuccess('添加成功！');
              emit('success');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        }
      }
    });
  };

  const emit = defineEmits(['update:open', 'success']);

  const handleClose = () => {
    emit('update:open', false);
  };

  if (props.opType == 0) {
    form.value.pid = props.id || 0;
  } else {
    form.value.id = props.id;
    title.value = '重命名';
    getData();
  }
</script>

<style lang="scss" scoped>
  :deep(.el-form-item) {
    width: 100% !important;
  }
</style>
