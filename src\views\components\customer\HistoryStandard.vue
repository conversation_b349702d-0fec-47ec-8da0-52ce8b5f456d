<template>
  <div class="custom-tab-container">
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="false"
      highlight-current-row
    >
      <template v-slot:empty>
        <bxc-empty />
      </template>
      <el-table-column label="序号" fixed min-width="60">
        <template #default="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="标准号"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span @click="handleDetail(row)" class="c-primary pointer">{{ row.standardCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="standardName"
        label="标准名称"
        show-overflow-tooltip
      />
      <el-table-column
        prop="standardTypeName"
        label="标准类型"
        show-overflow-tooltip
      />
      <el-table-column label="标准状态" show-overflow-tooltip>
        <template #default="{ row }">
          <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="publishDate"
        label="发布日期"
        show-overflow-tooltip
      />
      <el-table-column
        prop="executeDate"
        label="实施日期"
        show-overflow-tooltip
      />
      <el-table-column
        prop="updateTime"
        label="浏览日期"
        show-overflow-tooltip
      />
    </el-table>
    <pagination 
      v-show="total > 0" 
      :total="total" 
      v-model:page="queryParams.pageNum" 
      v-model:limit="queryParams.pageSize" 
      @pagination="getList" />
  </div>
</template>
<script setup>
import { getHistoryList } from '@/api/customer'
import { getStatusColor } from '@/utils'

const props = defineProps({
  type: {
    type: String,
    default:'0'
  }
})
const loading = ref(false)
const dataList = ref([])
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  module: props.type
})

const getList = () => {
  loading.value = true
  getHistoryList(queryParams.value).then(res => {
    dataList.value = res.rows || []
    total.value = res.total || 0
  }).finally(() => {
    loading.value = false
  })
}
const handleDetail = (row) => {
  if(props.type == '1'){
    window.open('/retrieval/internationDetail?id=' + row.relationId)
  }else{
    window.open('/retrieval/domesticDetail?id=' + row.relationId)
  }
}

getList()

</script>