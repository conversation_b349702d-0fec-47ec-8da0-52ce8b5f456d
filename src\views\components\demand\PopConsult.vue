<template>
  <div class="pop-container">
    <el-dialog :append-to-body="true" v-model="open" width="600px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="app-dialog">
        <el-form-item label="需求说明" prop="demandContent">
          <el-input
            v-model="form.demandContent"
            type="textarea"
            :rows="5"
            placeholder="请输入需求说明信息"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="单位名称" prop="unitName">
          <el-input
            v-model="form.unitName"
            placeholder="请输入单位名称"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item label="联系人" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入联系人"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入联系方式"
            maxlength="20"
          />
        </el-form-item>

        <div class="tip-box mt30">
          您也可以通过直接拨打我们的官方客服热线：<span class="mobile">************</span>，进行需求内容咨询。
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close" plain>关闭</el-button>
          <el-button @click.prevent="save" type="primary" :loading="loading">
            <span v-if="!loading">提交</span>
            <span v-else>提交中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { addConsult } from '@/api/demand/consult'
import useUserStore from '@/store/modules/user'
import { telMobileValidPattern } from '@/utils'

const { proxy } = getCurrentInstance()
const userStore = useUserStore()

const props = defineProps({
  open: Boolean,
});
const { open } = toRefs(props)

const data = reactive({
  title: '需求咨询',
  loading: false,
})

const {title,loading} = toRefs(data)

const form = ref({
  demandContent: undefined,
  unitName: userStore.userInfo.unitName || '',
  name: userStore.userInfo.realName || '',
  phone: userStore.phonenumber || ''
});
const rules = ref({
  demandContent: [{ required: true, message: '请输入需求说明', trigger: 'blur' }],
  name: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: telMobileValidPattern, message: '请输入正确的联系方式（手机号/座机号）', trigger: 'blur' },
  ],
});

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}

const save = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      data.loading = true
      addConsult(form.value).then(response => {
        emit('update:open',false);
        emit('success');
        proxy.$modal.msgSuccess("您所提交的需求咨询已收到，我们将有专员与您对接！");
      }).finally(() => {
        data.loading = false;
      });
    }
  });
}

</script>