import request from '@/utils/request'

// 修改用户信息
export function updateUserInfo(data) {
  return request({
    url: '/auth/cloudUser/profile',
    method: 'put',
    data,
  });
}
// 修改密码
export function updatePassword(data) {
  return request({
    url: '/auth/cloudUser/profile/updatePwd',
    method: 'put',
    data,
  });
}
// 修改手机号
export function updatePhonenumber(data) {
  return request({
    url: '/auth/cloudUser/profile/updatePhonenumber',
    method: 'put',
    data,
  });
}
// 获取CCS分类树状结构
export function getCCSTree(params) {
  return request({
    url: '/search/cloud/standardCcsInfo/tree',
    method: 'get',
    params,
  });
}
// 邮箱绑定
export function setEmail(data) {
  return request({
    url: '/auth/cloudUser/profile/updateEmail',
    method: 'put',
    data,
  });
}
// 设置短信提醒
export function setSmsRemind(data) {
  return request({
    url: '/auth/cloudUser/profile/updateIsNoteRemind',
    method: 'put',
    data,
  });
}
// 设置邮箱提醒
export function setEmailRemind(data) {
  return request({
    url: '/auth/cloudUser/profile/updateIsEmailRemind',
    method: 'put',
    data,
  });
}
// 设置站内消息提醒
export function setMsgRemind(data) {
  return request({
    url: '/auth/cloudUser/profile/updateIsMessageRemind',
    method: 'put',
    data,
  });
}
// 设置通知时间
export function setRemindTime(data) {
  return request({
    url: '/auth/cloudUser/profile/updateRemindTime',
    method: 'put',
    data,
  });
}
// 查询浏览历史列表
export function getHistoryList(params) {
  return request({
    url: '/search/browsingHistory/list',
    method: 'get',
    params,
  });
}