<template>
  <div class="tool-list">
    <div v-for="item in dataList" :key="item.key" @click="handleLink(item)" class="tool-item">
      <div class="item-icon">
        <img :src="item.img" alt="">
      </div>
      <div class="item-title">{{item.name}}</div>
    </div>
  </div>
</template>
<script setup>
import TOOL0 from "@/assets/images/workbench/tool-0.png"
import TOOL1 from "@/assets/images/workbench/tool-1.png"
import TOOL2 from "@/assets/images/workbench/tool-2.png"
import TOOL3 from "@/assets/images/workbench/tool-3.png"
import TOOL4 from "@/assets/images/workbench/tool-4.png"
import TOOL5 from "@/assets/images/workbench/tool-5.png"

const { proxy } = getCurrentInstance()

const dataList = ref([
  {
    name: "标准检索",
    img: TOOL0,
    key: "js",
    path: "/retrieval/domestic"
  },{
    name: "标准托管",
    img: TOOL1,
    key: "tg",
    path: "/data/trusteeship"
  },{
    name: "标准查新",
    img: TOOL2,
    key: "cx",
    path: "/data/search"
  },{
    name: "标准在线编写",
    img: TOOL3,
    key: "bx",
    path: ""
  },{
    name: "标准纠错",
    img: TOOL4,
    key: "jc",
    path: ""
  },{
    name: "标准动态",
    img: TOOL5,
    key: "dt",
    path: "/data/dynamic"
  },
])
const handleLink = (row) => {
  if (!row.path) {
    proxy.$modal.msgWarning("正在建设中，敬请期待");
  }else{
    location.href = row.path;
  }
};
</script>
<style lang="scss" scoped>
.tool-list{
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 25px 20px;
  .tool-item{
    flex: calc((100% - 100px)/3);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-width: 100px;
    height: 140px;
    background: linear-gradient(-37deg, #2F78FF, #E5EEFF);
    border-radius: 8px;
    box-sizing:border-box;
    cursor: pointer;
    .item-icon{
      height: 58px;
      img{
        height: 58px;
      }
    }
    .item-title{
      margin-top: 10px;
      font-size: 16px;
      color: #FFFFFF;
    }
  }
}
</style>