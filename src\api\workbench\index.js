import request from '@/utils/request';

// 查询用户收藏信息列表
export function getCollectionList(params) {
  return request({
    url: '/search/userCollectInfo/list',
    method: 'get',
    params
  })
}
// 工作台-标准推荐
export function getRecommendList(params) {
  return request({
    url: '/search/sdc/stdStandard/workbenchRecommended',
    method: 'get',
    params
  })
}
// 查询托管管理列表
export function getTrusteeshipList(params) {
  return request({
    url: '/search/trusteeshipManage/list',
    method: 'get',
    params
  })
}
// 获取工作台顶部统计列表数据
export function getOverviewStat(params) {
  return request({
    url: '/business/workbench/geTopStatisticList',
    method: 'get',
    params
  })
}