<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="formRef" label-width="auto" :inline="true">
        <el-form-item label="标准号" prop="standardCode">
          <el-input v-model="queryParams.standardCode" placeholder="请输入标准号检索" @keyup.enter="getData('pageNum')" />
        </el-form-item>
        <el-form-item label="标准名称" prop="standardName">
          <el-input v-model="queryParams.standardName" placeholder="请输入标准名称检索" @keyup.enter="getData('pageNum')" />
        </el-form-item>
        <el-form-item label="标准类型" prop="standardType">
          <el-select v-model="queryParams.standardType" placeholder="请选择标准类型" clearable>
            <el-option
              v-show="dict.value != 5 && dict.value != 6"
              v-for="dict in bxc_standard_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标准状态" prop="standardStatus">
          <el-select v-model="queryParams.standardStatus" placeholder="请选择标准状态" clearable>
            <el-option v-for="dict in bxc_standard_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" icon="Search" @click="getData('pageNum')">查询</el-button>
          <el-button plain icon="Refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="bgc-ff mt15">
      <div class="app-tabs">
        <div class="flex">
          <div
            @click="handleTabs(index)"
            v-for="(item, index) in tabsList"
            :key="index"
            class="app-tabs-item"
            :class="{ 'app-tabs-active': index == activeTab }"
          >
            {{ item }}
          </div>
        </div>
        <div class="flex">
          <el-button @click="handleClick('jump', '/retrieval/domestic')">
            <span class="iconfont icon-tuoguan f-14 mr5"></span>
            托管标准
          </el-button>
          <el-button @click="handleClick('batch')" class="ml10">
            <span class="iconfont icon-piliangcaozuo f-14 mr5"></span>
            批量托管
          </el-button>
          <el-button @click="handleClick('cancel')" :disabled="!currentRow" class="ml10">
            <span class="iconfont icon-quxiao f-14 mr5"></span>
            取消托管
          </el-button>
        </div>
      </div>
      <div class="app-container-content">
        <div class="flex flex-sb">
          <div v-if="statInfo.count > 0 && activeTab == 0" class="overview-wrap">
            <div v-if="statInfo.count > 0">
              托管标准：
              <span class="num">{{ statInfo.count }}</span>
            </div>
            <div v-if="statInfo.countryCount > 0">
              国家标准：
              <span class="num">{{ statInfo.countryCount }}</span>
            </div>
            <div v-if="statInfo.industryCount > 0">
              行业标准：
              <span class="num">{{ statInfo.industryCount }}</span>
            </div>
            <div v-if="statInfo.localCount > 0">
              地方标准：
              <span class="num">{{ statInfo.localCount }}</span>
            </div>
            <div v-if="statInfo.groupCount > 0">
              团体标准：
              <span class="num">{{ statInfo.groupCount }}</span>
            </div>
            <div v-if="statInfo.enterpriseCount > 0">
              企业标准：
              <span class="num">{{ statInfo.enterpriseCount }}</span>
            </div>
            <div v-if="statInfo.metrologicalCount > 0">
              计量技术法规：
              <span class="num">{{ statInfo.metrologicalCount }}</span>
            </div>
            <div v-if="statInfo.normativeCount > 0">
              规范性文件：
              <span class="num">{{ statInfo.normativeCount }}</span>
            </div>
          </div>
          <div  v-if="statInfo.repealCount > 0 || statInfo.replaceCount > 0" v-show="activeTab == 1" class="overview-wrap">
            <div v-if="statInfo.repealCount > 0">
              废止标准：
              <span class="num">{{ statInfo.repealCount }}</span>
            </div>
            <div v-if="statInfo.replaceCount > 0">
              被替代标准：
              <span class="num">{{ statInfo.replaceCount }}</span>
            </div>
          </div>
        </div>
        <el-table
          ref="singleTableRef"
          v-loading="loading"
          :data="tableData"
          :border="false"
          highlight-current-row
          @current-change="handleCurrentChange"
          class="mt15"
        >
          <template v-slot:empty>
            <bxc-empty class="mt30" />
          </template>
          <el-table-column label="序号" fixed width="90">
            <template #default="{ $index }">
              {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="标准号" min-width="200" fixed="left" show-overflow-tooltip>
            <template #default="{ row }">
              <span @click="handleClick('jump', '/retrieval/domesticDetail?id=' + row.standardId)" class="c-primary pointer">
                {{ row.standardCode }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="standardName" label="标准名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="standardTypeName" label="标准类型" min-width="100" show-overflow-tooltip />
          <el-table-column label="标准状态" min-width="100" show-overflow-tooltip>
            <template #default="{ row }">
              <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="activeTab == 0" prop="publishDate" label="发布日期" min-width="180" show-overflow-tooltip />
          <el-table-column prop="executeDate" label="实施日期" min-width="180" show-overflow-tooltip />
          <el-table-column label="废止日期" min-width="180" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="c-FF0000">{{ row.repealDate }}</span>
            </template>
          </el-table-column>
          <template v-if="activeTab == 1">
            <el-table-column prop="beReplacedStandardCode" label="被替代标准号" min-width="200" show-overflow-tooltip />
            <el-table-column prop="beReplacedStandardName" label="被替代标准名称" min-width="200" show-overflow-tooltip />
          </template>
          <el-table-column prop="createTime" label="托管日期" min-width="180" show-overflow-tooltip />
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getData"
        />
      </div>
    </div>
    <upload-dialog v-if="uploadVisible" v-model:visible="uploadVisible" @uploadSuccess="uploadSuccess" />
    <result-dialog
      v-if="resultVisible"
      v-model:visible="resultVisible"
      v-model:resultData="resultData"
      @updateData="getData('pageNum')"
    />
  </div>
</template>
<script setup>
  import { getTrusteeship, removeTrusteeship } from '@/api/trusteeship/index';
  import UploadDialog from '@/views/components/trusteeship/UploadDialog.vue';
  import ResultDialog from '@/views/components/trusteeship/ResultDialog.vue';
  import { getStatusColor } from '@/utils';
  
  const { proxy } = getCurrentInstance();
  const { bxc_standard_type, bxc_standard_status } = proxy.useDict('bxc_standard_type', 'bxc_standard_status');

  const singleTableRef = ref(null);
  const loading = ref(false);
  const activeTab = ref(0);
  const tabsList = reactive(['托管标准', '托管预警']);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const statInfo = ref({});
  const tableData = ref([]);
  const total = ref(0);
  const currentRow = ref();
  const uploadVisible = ref(false);
  const resultVisible = ref(false);
  const resultData = ref({});

  const getData = data => {
    loading.value = true;
    if (data) queryParams.value.pageNum = 1;
    queryParams.value.queryType = activeTab.value;
    getTrusteeship(queryParams.value)
      .then(res => {
        tableData.value = res.rows || [];
        total.value = res.total;
        statInfo.value = res.bean || {};
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleReset = () => {
    proxy.$refs.formRef.resetFields();
    getData('pageNum');
  };

  const handleClick = (type, data) => {
    switch (type) {
      case 'jump':
        window.open(window.location.origin + data, '_blank');
        break;
      case 'cancel':
        proxy
          .$confirm(
            '确认取消托管标准【' + currentRow.value?.standardCode + ' | ' + currentRow.value?.standardName + '】？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
          .then(() => {
            removeTrusteeship({ ids: [currentRow.value?.standardId] }).then(res => {
              singleTableRef.value.setCurrentRow();
              proxy.$modal.msgSuccess('取消托管成功！');
              getData();
            });
          })
          .catch(() => {});
        break;
      case 'batch':
        uploadVisible.value = true;
        break;
      default:
        break;
    }
  };

  const handleTabs = index => {
    if (index == activeTab.value) return;
    activeTab.value = index;
    handleReset();
  };

  const handleCurrentChange = val => {
    currentRow.value = val;
  };

  const uploadSuccess = data => {
    resultVisible.value = true;
    resultData.value = data;
  };

  getData();
</script>

<style lang="scss" scoped>
  .app-tabs {
    padding: 5px 30px 0 10px;
    box-sizing: border-box;
  }

  :deep(.el-button:focus) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:hover) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }

  :deep(.el-button:active) {
    color: var(--el-button-text-color) !important;
    border-color: var(--el-button-border-color) !important;
    background-color: var(--el-button-bg-color) !important;
    outline: 0;
  }
</style>
