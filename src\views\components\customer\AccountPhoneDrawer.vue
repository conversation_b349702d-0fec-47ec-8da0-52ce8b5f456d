<template>
  <el-drawer title="安全手机号设置" size="480" append-to-body v-model="props.open" :close-on-click-modal="false" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="app-dialog">
      <div class="mobile-box mt10">
        <div class="mobile-left">
          手机号码: <span>{{userStore.phonenumber}}</span>
        </div>
        <div class="mobile-right">
          <div v-if="!time ||time == 0" @click="handleVerify" class="code-box">获取验证码</div>
          <div v-else class="down-timer">{{time}}s 后重新获取</div>
        </div>
      </div>
      <el-form-item class="mt20" label="验证码" prop="oldSmsCode">
        <el-input
          size="large"
          v-model="form.oldSmsCode"
          placeholder="请输入验证码"
          maxlength="6"
        />
      </el-form-item>
      <el-form-item label="安全手机号码" prop="newPhonenumber">
        <el-input
          v-model="form.newPhonenumber"
          type="text"
          size="large"
          auto-complete="off"
          placeholder="请设置安全手机号码"
          maxlength="11"
        >
          <template #append>
            <div v-if="!newTime ||newTime == 0" @click="handleNewVerify" class="verify">获取验证码</div>
            <div v-else class="down-timer">{{newTime}}s 后重新获取</div>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item class="mt20" label="验证码" prop="newSmsCode">
        <el-input
          size="large"
          v-model="form.newSmsCode"
          placeholder="请输入验证码"
          maxlength="6"
        />
      </el-form-item>

      <el-form-item class="mt50">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          style="width:100%;"
          @click.prevent="handleSave"
        >
          <span v-if="!loading">保 存</span>
          <span v-else>保 存 中...</span>
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="loading"
          size="large"
          style="width:100%;"
          plain
          @click.prevent="handleClose"
        >
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script setup>
import { updatePhonenumber } from '@/api/customer'
import useUserStore from '@/store/modules/user'
import { mobileValidPattern  } from '@/utils'
import useMobileCode from "@/composables/useMobileCode";

const { proxy } = getCurrentInstance();
const userStore = useUserStore()
const { time, getMobileCode, newTime, getNewMobileCode } = useMobileCode()

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  }
});

const loading = ref(false);

const form = ref({
  phonenumber: userStore.phonenumber,
  newPhonenumber: "",
  newSmsCode: "",
  oldSmsCode: ""
});
const checkPassword = (rule, value, callback) => {
  if (value == userStore.phonenumber) {
    callback(new Error("新安全手机号码与原号码不可相同"));
  } else {
    callback();
  }
}
const rules = ref({
  newPhonenumber: [
    { required: true, trigger: "blur", message: "请输入手机号码" },
    { validator: checkPassword, trigger: "blur" },
    { pattern: mobileValidPattern, trigger: "blur", message: "请输入正确的手机号码" }
  ],
  oldSmsCode: [{ required: true, trigger: "blur", message: "请输入验证码" }],
  newSmsCode: [{ required: true, trigger: "blur", message: "请输入验证码" }],
});

const emit = defineEmits(['update:open','success']);

const handleVerify = () => {
  if(time.value == 0) {
    if (userStore.phonenumber) {
      let isSend = false; // 放重复点击
      if(isSend) return;
      
      getMobileCode(userStore.phonenumber, 5).then(res => {
        isSend = true;
      }).finally(() => {
        isSend = false;
      })
    }
  }
}
const handleNewVerify = () => {
  if (form.value.newPhonenumber == userStore.phonenumber) {
    proxy.$modal.msgError("新安全手机号码与原号码不可相同");
    return 
  }
  if(newTime.value == 0) {
    proxy.$refs.formRef.validateField("newPhonenumber", (valid) => {
      if (valid) {
        let isSend = false; // 放重复点击
        if(isSend) return;
        
        getNewMobileCode(form.value.newPhonenumber, 6).then(res => {
          isSend = true;
        }).finally(() => {
          isSend = false;
        })
      }
    });
  }
  
}
const handleSave = () => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      updatePhonenumber(form.value).then(res => {
        proxy.$modal.msgSuccess('安全手机号修改成功！');
        emit('success');
        emit('update:open', false);
      }).finally(() => {
        loading.value = false;
      });
    }
  });
}

const handleClose = () => {
  emit('update:open', false);
};

</script>
