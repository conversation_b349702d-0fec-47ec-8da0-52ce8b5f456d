import request from '@/utils/request';

// 产业
export function getNationalEconomicTypeTree(params) {
  return request({
    url: '/search/nationalEconomicType/tree',
    method: 'get',
    params,
  });
}

// 列表
export function getCollectList(params) {
  return request({
    url: '/search/userCollectInfo/list',
    method: 'get',
    params,
  });
}

// 更新状态
export function upadteCollect(data) {
  return request({
    url: '/search/userCollectInfo/isCollect',
    method: 'post',
    data,
  });
}
