<template>
  <div class="app-container">
    <el-tabs v-model="activeName" class="custom-tabs" @tab-click="handleClick">
      <el-tab-pane label="标准" name="0">
        <history-standard v-if="activeName == '0'" type="0" />
      </el-tab-pane>
      <el-tab-pane label="国际国外标准" name="1">
        <history-standard v-if="activeName == '1'" type="1" />
      </el-tab-pane>
      <el-tab-pane label="TC目录" name="2">
        <history-tc v-if="activeName == '2'" />
      </el-tab-pane>
      <el-tab-pane label="标准样品" name="3">
        <history-sample v-if="activeName == '3'" />
      </el-tab-pane>
      <el-tab-pane label="标准专家" name="4">
        <history-expert v-if="activeName == '4'" />
      </el-tab-pane>
      <el-tab-pane label="标准公告" name="5">
        <history-notice v-if="activeName == '5'" />
      </el-tab-pane>
      <el-tab-pane label="法律法规" name="6">
        <history-law v-if="activeName == '6'" />
      </el-tab-pane>
      <el-tab-pane label="计划标准" name="7">
        <history-plan v-if="activeName == '7'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup>
import HistoryStandard from '@/views/components/customer/HistoryStandard'
import HistoryTc from '@/views/components/customer/HistoryTc'
import HistorySample from '@/views/components/customer/HistorySample'
import HistoryNotice from '@/views/components/customer/HistoryNotice'
import HistoryLaw from '@/views/components/customer/HistoryLaw'
import HistoryPlan from '@/views/components/customer/HistoryPlan'
import HistoryExpert from '@/views/components/customer/HistoryExpert'


const activeName = ref('0')

const handleClick = (tab) => {
  
}

</script>