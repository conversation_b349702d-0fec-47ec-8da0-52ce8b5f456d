<template>
  <div v-resize="onResize" :class="className" :style="{height:height,width:width}" />
</template>

<script setup>
import * as echarts from 'echarts'
// import resize from '@/views/dashboard/mixins/resize'

const {proxy} = getCurrentInstance()
// mixins: [resize],
const props = defineProps({
  className: {
    type: String,
    default: 'chart'
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: "100%"
  },
  autoResize: {
    type: Boolean,
    default: true
  },
  chartData: {
    type: Object,
    required: true
  }
})
const {className,width,height,autoResize,chartData} = toRefs(props)

const data = reactive({
  chart: null
})

const {chart} = toRefs(data)

watch(()=>props.chartData,(newVal)=>{
  setOptions(newVal)
},{deep: true})

onMounted(()=>{
  proxy.$nextTick(() => {
    initChart()
  })
})
onBeforeUnmount(() => {
  if (!data.chart) {
    return
  }
  data.chart.dispose()
  data.chart = null
})

const initChart = () => {
  data.chart = markRaw(echarts.init(proxy.$el))
  setOptions(props.chartData)
}
const colors = ['#045CFF', '#00FAA8', '#F69D0D', '#EFEF61', '#D537ED', '#641BCE', '#0336FF']
let series = [];
const setSeries = () => {
  series = []
  props.chartData.nodeData.map(item => {
    series.push({
      name: item.name,
      type: 'bar',
      // stack: "total",
      animationDuration: 2800,
      animationEasing: 'cubicInOut',
      barWidth:22,
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: '#045CFF' // 0% 处的颜色
          }, {
            offset: 1,
            color: '#00AEFF' // 100% 处的颜色
          }], false),
          barBorderRadius: [30, 30, 0, 0],
        }
      },
      data: item.echartsData,
    })
  })
}
const setOptions = ({ dmList, nodeData } = {}) => {
  if(!dmList) return;
  setSeries()
  data.chart.setOption({
    xAxis: {
      data: dmList,
      boundaryGap: true,
      axisTick: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#E8E8E8'
        }
      },
      axisLabel: {
        color: '#666666'
      }
    },
    tooltip: {
      show: true,
      trigger: 'axis'
    },
    grid: {
      left: 30,
      right: 50,
      bottom: 20,
      top: 30,
      containLabel: true
    },
    yAxis: {
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: ['#E8E8E8']
        }
      },
      axisLabel: {
        color: '#666666'
      }
    },
    color: colors,
    dataZoom: [
      {
        show: dmList.length > 35,
        type: 'slider',
        startValue: dmList.length - 35,
        endValue: dmList.length,
      },
      {
        show: dmList.length > 35,
        start: dmList.length - 35,
        end: dmList.length,
      },
    ],
    series: series
  })
}
const onResize = () => {
  data.chart && data.chart.resize()
}
</script>
