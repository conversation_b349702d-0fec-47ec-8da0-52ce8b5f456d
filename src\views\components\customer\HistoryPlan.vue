<template>
  <div class="custom-tab-container">
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="false"
      highlight-current-row
    >
      <template v-slot:empty>
        <bxc-empty />
      </template>
      <el-table-column label="序号" fixed min-width="60">
        <template #default="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="计划号"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span @click="handleDetail(row)" class="c-primary pointer">{{ row.planNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="entryName"
        label="项目名称"
        show-overflow-tooltip
      />
      <el-table-column
        prop="typeName"
        label="计划类型"
        show-overflow-tooltip
      />
      <el-table-column
        prop="amendName"
        label="制修订"
        show-overflow-tooltip
      />
      <el-table-column
        prop="planReleaseDate"
        label="下达日期"
        show-overflow-tooltip
      />
      <el-table-column
        prop="registryUnit"
        label="归口单位"
        show-overflow-tooltip
      />
      <el-table-column
        prop="updateTime"
        label="浏览日期"
        show-overflow-tooltip
      />
    </el-table>
    <pagination 
      v-show="total > 0" 
      :total="total" 
      v-model:page="queryParams.pageNum" 
      v-model:limit="queryParams.pageSize" 
      @pagination="getList" />
  </div>
</template>
<script setup>
import { getHistoryList } from '@/api/customer'

const loading = ref(false)
const dataList = ref([])
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  module: '7'
})

const getList = () => {
  loading.value = true
  getHistoryList(queryParams.value).then(res => {
    dataList.value = res.rows || []
    total.value = res.total || 0
  }).finally(() => {
    loading.value = false
  })
}
const handleDetail = (row) => {
  window.open('/retrieval/planDetail?id=' + row.relationId)
}

getList()

</script>