import request from '@/utils/request';

export function getFillingData(params) {
  return request({
    url: '/business/fillingData/queryByName',
    method: 'get',
    params,
  });
}

export function getTopStatistic(params) {
  return request({
    url: '/business/fillingData/topStatistic',
    method: 'get',
    params,
  });
}

export function getNationalEconomicRank(params) {
  return request({
    url: '/business/fillingData/nationalEconomicRank',
    method: 'get',
    params,
  });
}

export function getStandardizationEnterprisesProportion(params) {
  return request({
    url: '/business/fillingData/standardizationEnterprisesProportion',
    method: 'get',
    params,
  });
}

export function getStandardizedStaffStatistics(params) {
  return request({
    url: '/business/fillingData/standardizedStaffStatistics',
    method: 'get',
    params,
  });
}

export function getStandardizedEngineerStatistics(params) {
  return request({
    url: '/business/fillingData/standardizedEngineerStatistics',
    method: 'get',
    params,
  });
}

export function getStandardizedActivityStatistics(params) {
  return request({
    url: '/business/fillingData/standardizedActivityStatistics',
    method: 'get',
    params,
  });
}

export function getFillingDataList(params) {
  return request({
    url: '/business/fillingData/list',
    method: 'get',
    params,
  });
}

export function getFillingDataDetail(id) {
  return request({
    url: '/business/fillingData/' + id,
    method: 'get',
  });
}
