<template>
  <div v-loading="loading" class="recommend-wrap">
    <div v-if="dataList && dataList.length > 0" class="recommend-list">
      <div v-for="item in dataList" :key="item.id" @click="handleDetail(item)" class="recommend-item">
        <div class="icon" :class="getTypeClass(item.standardType)">{{getTypeIcon(item.standardType)}}</div>
        <div class="title">{{ item.standardCode + ' | ' + item.standardName }}</div>
      </div>
    </div>
    <bxc-empty v-else />
  </div>
</template>
<script setup>
import { getRecommendList } from '@/api/workbench'

const loading = ref(false)
const dataList = ref([])
const total = ref(0)
const queryParams = ref({
  pageNum: 1,
  pageSize: 9,
})

const getList = () => {
  loading.value = true
  getRecommendList(queryParams.value).then(res => {
    dataList.value = res.data || []
    total.value = res.total
  }).finally(() => {
    loading.value = false
  })
}
const handleDetail = (row) => {
  window.open('/retrieval/domesticDetail?id=' + row.id)
}

const getTypeIcon = (type) => {
  switch (type) {
    case '0':
      return '国'
    case '1':
      return '行'
    case '2':
      return '地'
    case '3':
      return '团'
    case '4':
      return '企'
    default:
      return '标'
  }
}
const getTypeClass = (type) => {
  switch (type) {
    case '0':
      return 'bgc-primary'
    case '1':
      return 'green'
    case '2':
      return 'cyan'
    case '3':
      return 'red'
    case '4':
      return 'yellow'
    default:
      return 'bgc-primary'
  }
}

getList()
</script>
<style lang="scss" scoped>
.recommend-wrap{
  width: 100%;
  height: 100%;
}
.recommend-list {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  .recommend-item{
    height: 45px;
    display: flex;
    align-items: center;
    .icon{
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      background: $primary-color;
      border-radius: 2px;
      color: #FFFFFF;
      font-size: 12px;
      display: flex;
      justify-content:center;
      align-items: center;
    }
    .red{
      background: #FF0000;
    }
    .green{
      background: #04AE00;
    }
    .yellow{
      background: #FFAF04;
    }
    .cyan{
      background: #00C7F2;
    }
    .title{
      margin-left: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      &:hover{
        color: $primary-color;
      }
    }
  }
}

</style>