<template>
  <div>
    <div v-if="props.total > 0" class="flex flex-sb mb15">
      <div class="overview-wrap">
        <div>
          收藏标准计划：
          <span class="num">{{ props.total }}</span>
        </div>
        <div v-if="bean.countryStandardPlan > 0">
          国家标准计划：
          <span class="num">{{ bean.countryStandardPlan }}</span>
        </div>
        <div v-if="bean.industryStandardPlan > 0">
          行业标准计划：
          <span class="num">{{ bean.industryStandardPlan }}</span>
        </div>
        <div v-if="bean.localStandardPlan > 0">
          地方标准计划：
          <span class="num">{{ bean.localStandardPlan }}</span>
        </div>
        <div v-if="bean.associationStandardPlan > 0">
          团体标准计划：
          <span class="num">{{ bean.associationStandardPlan }}</span>
        </div>
      </div>
    </div>
    <el-table v-loading="loading" :data="tableData" :border="false">
      <template v-slot:empty>
        <bxc-empty class="mt30" />
      </template>
      <el-table-column label="序号" fixed width="90">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="计划号" min-width="200" fixed="left" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('jump', '/retrieval/planDetail?id=' + row.recordId)" class="c-primary pointer">
            {{ row.planNumber }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="entryName" label="项目名称" min-width="200" show-overflow-tooltip />
      <el-table-column prop="typeName" label="计划类型" min-width="180" show-overflow-tooltip />
      <el-table-column prop="amendName" label="制修订" min-width="180" show-overflow-tooltip />
      <el-table-column prop="planReleaseDate" label="下达日期" min-width="180" show-overflow-tooltip />
      <el-table-column prop="registryUnit" label="归口单位" min-width="180" show-overflow-tooltip />
      <el-table-column label="操作" min-width="100"  fixed="right" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('cancel', row)" class="c-primary pointer">取消收藏</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
  import { upadteCollect } from '@/api/collect';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    bean: {
      type: Object,
      default: () => {
        return {};
      },
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    total: {
      type: [String, Number],
      default: 0,
    },
    queryParams: {
      type: Object,
    },
  });

  const { loading, bean, tableData, queryParams } = toRefs(props);

  const handleClick = (type, data) => {
    switch (type) {
      case 'jump':
        window.open(data, '_blank');
        break;
      case 'cancel':
        proxy
          .$confirm('确认取消收藏当前数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            upadteCollect({ id: data.id, recordId: data.recordId, recordType: 7, isCollect: 0 }).then(res => {
              proxy.$modal.msgSuccess('取消收藏成功！');
              emit('updateData');
            });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  const emit = defineEmits(['updateData']);
</script>

<style lang="scss" scoped></style>
