<template>
  <div>
    <div class="statistics">
      <div class="statistics-top">
        <div>提交日期</div>
        <el-date-picker
          v-model="dateRange"
          :disabled-date="disabledDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
        <el-button type="primary" icon="Search" @click="getChartData">查询</el-button>
        <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
      <div class="statistics-card">
        <div v-for="(item, index) in cardList" :key="index" class="statistics-card-item">
          <div class="statistics-card-item-title overflow-ellipsis">{{ item.name }}</div>
          <div class="statistics-card-item-num overflow-ellipsis">{{ quantityStatistics[item.fieldName] || 0 }}</div>
        </div>
      </div>
    </div>
    <div class="chart">
      <div class="chart-item">
        <div class="chart-item-title">填报企业行业类别排名Top10</div>
        <div class="chart-item-content">
          <stat-crosswise-bar-chart
            v-if="rankingData && rankingData.dmList && rankingData.dmList.length > 0"
            :chart-data="rankingData"
            :height="'470px'"
          />
          <bxc-empty height="470px" v-else />
        </div>
      </div>
      <div class="chart-item">
        <div class="chart-item-title">设置标准化部门企业占比</div>
        <div class="chart-item-content">
          <stat-pie-chart v-if="departmentData && departmentData.length > 0" :chart-data="departmentData" :height="'470px'" />
          <bxc-empty v-else height="470px" />
        </div>
      </div>
      <div class="chart-item">
        <div class="chart-item-title">标准化工作人员统计</div>
        <div class="chart-item-content">
          <stat-bar-chart
            v-if="workerData && workerData.dmList && workerData.dmList.length > 0"
            :chart-data="workerData"
            :height="'470px'"
          />
          <bxc-empty v-else height="470px" />
        </div>
      </div>
      <div class="chart-item">
        <div class="chart-item-title">标准化工程师统计</div>
        <div class="chart-item-content">
          <stat-bar-chart
            v-if="engineerData && engineerData.dmList && engineerData.dmList.length > 0"
            :chart-data="engineerData"
            :height="'470px'"
          />
          <bxc-empty v-else height="470px" />
        </div>
      </div>
      <div class="chart-item">
        <div class="chart-item-title">标准化活动统计</div>
        <div class="chart-item-content">
          <stat-pie-chart v-if="activityData && activityData.length > 0" :chart-data="activityData" :height="'470px'" />
          <bxc-empty v-else height="470px" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    getTopStatistic,
    getNationalEconomicRank,
    getStandardizationEnterprisesProportion,
    getStandardizedStaffStatistics,
    getStandardizedEngineerStatistics,
    getStandardizedActivityStatistics,
  } from '@/api/standard-stat/fill-in';
  import { ElLoading } from 'element-plus';
  import StatCrosswiseBarChart from '@/views/components/standard-stat/fill-stat/StatCrosswiseBarChart.vue';
  import StatPieChart from '@/views/components/standard-stat/fill-stat/StatPieChart.vue';
  import StatBarChart from '@/views/components/standard-stat/fill-stat/StatBarChart.vue';

  const route = useRoute();

  let loadingInstance;
  const dateRange = ref([]);
  const cardList = ref([
    { name: '填报数（个）', fieldName: 'fillingCount' },
    { name: '填报企业（家）', fieldName: 'enterpriseCount' },
    { name: '标准化经费投入总额（万元）', fieldName: 'projectFundsTotalAmount' },
    { name: '标准化工作人员（人）', fieldName: 'staffNum' },
    { name: '标准化工程师（人）', fieldName: 'engineerNum' },
    { name: '参加标准化活动（项）', fieldName: 'activityNum' },
    { name: '主导参与制修订企业（家）', fieldName: 'editEnterpriseCount' },
    { name: '主导参与制修订标准（个）', fieldName: 'standardEditCount' },
  ]);
  const quantityStatistics = ref({});
  const rankingData = ref({});
  const departmentData = ref([]);
  const activityData = ref([]);
  const workerData = ref({});
  const engineerData = ref({});

  const disabledDate = time => {
    return time.getTime() > new Date().setHours(0, 0, 0, 0);
  };

  const resetQuery = () => {
    dateRange.value = [];
    getChartData()
  };

  const getChartData = () => {
    loadingInstance = ElLoading.service({ text: '正在加载，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
    let queryParams = {
      taskId: route.query.id,
      startTime: dateRange.value && dateRange.value.length > 0 ? dateRange.value[0] : '',
      endTime: dateRange.value && dateRange.value.length > 0 ? dateRange.value[1] : '',
    };
    getTopStatistic(queryParams).then(res => {
      quantityStatistics.value = res.data || {};
    });
    getNationalEconomicRank(queryParams).then(res => {
      rankingData.value = res.data || {};
    });
    getStandardizationEnterprisesProportion(queryParams).then(res => {
      departmentData.value = res.data || [];
    });
    getStandardizedStaffStatistics(queryParams).then(res => {
      workerData.value = res.data || {};
    });
    getStandardizedEngineerStatistics(queryParams).then(res => {
      engineerData.value = res.data || {};
    });
    getStandardizedActivityStatistics(queryParams).then(res => {
      activityData.value = res.data || [];
    });
    nextTick(() => {
      loadingInstance.close();
    });
  };

  getChartData();
</script>

<style lang="scss" scoped>
  .statistics {
    width: 100%;
    background-color: #fff;
    padding: 25px;
    box-sizing: border-box;
    margin-top: 15px;

    &-top {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333333;
    }

    &-top > :nth-child(n + 2) {
      margin-left: 15px;
    }

    &-card {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      margin-top: 5px;

      &-item {
        margin: 20px 15px 0 0;
        width: calc((100% - 15px * 5) / 6);
        height: 110px;
        background-image: url('@/assets/images/standard-stat/fill-card.png');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center center;
        border-radius: 5px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 25px;
        box-sizing: border-box;
        color: #fff;

        &:nth-child(6n) {
          margin-right: 0;
        }

        &-title {
          font-size: 14px;
        }

        &-num {
          font-size: 32px;
          font-weight: bold;
          margin-top: 20px;
        }
      }
    }
  }

  .chart {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    &-item {
      width: 49.5%;
      background-color: #fff;
      margin-top: 15px;
      padding: 30px 25px;
      box-sizing: border-box;

      &:nth-child(even) {
        margin-left: 1%;
      }

      &-title {
        font-weight: 600;
        font-size: 18px;
        color: #333333;
      }

      &-content {
        margin-top: 50px;
      }
    }
  }

  :deep(.el-input__wrapper) {
    height: 36px !important;
    flex-grow: 0 !important;
  }

  :deep(.el-button) {
    height: 36px !important;
    line-height: 36px !important;
  }
</style>
