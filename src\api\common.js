import request from '@/utils/request';

// 上传
export function upload(data) {
  return request({
    url: '/resource/oss/upload',
    method: 'post',
    data: data,
  });
}

// 省市区
export function getDistrictList() {
  return request({
    url: '/system/districts/getDistrictsList',
    method: 'get',
  });
}

// 成员树
export function memberTree(data) {
  return request({
    url: '/systree/knowTree',
    method: 'get',
    params: data
  })
}

// 收藏/取消收藏
export const userCollect = data => {
  return request({
    url: '/process/userCollectInfo/isCollect',
    method: 'post',
    data,
  });
};

// 在线人员数
export function getOnlineTotal() {
  return request({
    url: '/monitor/online/total',
    method: 'get'
  })
}

// 查询用户列表，用于用户选择场景
export function getUserList(params) {
  return request({
    url: '/system/user/selectUser',
    method: 'get',
    params
  })
}

// 查询用户列表，用于用户选择场景
export function getApproveStatus(params) {
  return request({
    url: '/system/config/configKey/standard.approval.config',
    method: 'get'
  })
}
// 根据云平台用户 查询授权
export function getAccredit() {
  return request({
    url: '/auth/cloudUser/queryAccreditByUserId',
    method: 'get'
  })
}