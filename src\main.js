import { createApp } from 'vue'

import Cookies from 'js-cookie'

import ElementPlus from 'element-plus'
import locale from 'element-plus/lib/locale/lang/zh-cn' // 中文语言

import '@/assets/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive


// 注册指令
import plugins from './plugins' // plugins
import { download } from '@/utils/request'

// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

// highlight 的样式，依赖包，组件
import hljs from "highlight.js/lib/core"
import xml from "highlight.js/lib/languages/xml"
import json from "highlight.js/lib/languages/json"
hljs.registerLanguage("xml", xml)
hljs.registerLanguage("json", json)
import 'highlight.js/styles/atom-one-dark.css'
import "highlight.js/lib/common"
import hljsVuePlugin from "@highlightjs/vue-plugin"

import './permission' // permission control

import { useDict } from '@/utils/dict'
import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel, selectDictLabels } from '@/utils/ruoyi'

// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 字典标签组件
import DictTag from '@/components/DictTag'
// 空状态
import Empty from '@/components/Empty'
import BxcEmpty from '@/components/BxcEmpty'
//aes 加密
import Aes from "@/utils/aes"

const app = createApp(App)

// 全局方法挂载
app.config.globalProperties.useDict = useDict
app.config.globalProperties.download = download
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.selectDictLabels = selectDictLabels
app.config.globalProperties.aes = Aes

// 全局过滤器
app.config.globalProperties.$filters = {
  money(data) {
    if (!data) return '0.00'
    // 将数据分割，保留两位小数
    if(typeof data == 'string'){
      data = Number(data)
    }
    data = data.toFixed(2)
    // 获取整数部分
    const intPart = Math.trunc(data)
    // 整数部分处理，增加,
    const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    // 预定义小数部分
    let floatPart = '.00'
    // 将数据分割为小数部分和整数部分
    const newArr = data.toString().split('.')
    if (newArr.length === 2) {
      floatPart = newArr[1].toString()
      return intPartFormat + '.' + floatPart
    }
    return intPartFormat + floatPart
  }
}

// 全局组件挂载
app.component('DictTag', DictTag)
app.component('Pagination', Pagination)
app.component('TreeSelect', TreeSelect)
app.component('FileUpload', FileUpload)
app.component('ImageUpload', ImageUpload)
app.component('ImagePreview', ImagePreview)
app.component('RightToolbar', RightToolbar)
app.component('Empty', Empty)
app.component('BxcEmpty', BxcEmpty)

app.use(router)
app.use(store)
app.use(plugins)
app.use(elementIcons)
app.component('svg-icon', SvgIcon)
app.use(hljsVuePlugin)

directive(app)

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get('size') || 'default'
})

app.mount('#app')
