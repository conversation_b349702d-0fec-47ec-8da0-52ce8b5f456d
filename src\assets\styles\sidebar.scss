#app {
  .header-wrap {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    line-height: 60px;
    background: #FFFFFF !important;
    box-shadow: 0px 2px 5px 0px rgba(8,22,47,0.08);
    color: #333333;
    font-size: 16px;
    z-index: 999;
    min-width: 1200px;
    .header-container {
      height: 100%;
      line-height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 30px;
      .header-left {
        height: 100%;
        line-height: 100%;
        display: flex;
        align-items: center;
        a {
          line-height: 100%;
          display: flex;
          img {
            height: 36px;
          }
        }
        div:hover{
          color: $primary-color;
        }
      }
      .header-right {
        margin-left: 20px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .header-avatar{
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-left: 20px;
          cursor: pointer;
        }
        .header-login {
          color: #333333;
          padding: 0 15px;
          &:hover{
            color: $primary-color;
          }
        }
        .header-register {
          color: #FFFFFF;
          background: $primary-color;
          padding: 0 15px;
        }
        a{
          height: 60px;
          line-height: 60px;
        }
      }
    }
  }
  .app-wrap{
    position: relative;
    height: 100%;
  }
  .content-wrap{
    position: relative !important;
    height: calc(100% - 60px) !important;
  }
  .main-container {
    height: 100%;
    transition: margin-left 0.28s;
    margin-left: $base-sidebar-width;
    margin-top: 60px;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    -webkit-transition: width 0.28s;
    transition: width 0.28s;
    width: $base-sidebar-width !important;
    background-color: $base-menu-background;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 60px;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    -webkit-box-shadow: 2px 3px 6px rgba(0, 21, 41, 0.35);
    box-shadow: 2px 3px 6px rgba(0, 21, 41, 0.35);
    box-sizing: border-box;
    padding: 15px 0px;
    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    .el-menu-item,
    .el-submenu__title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      padding-left: calc(var(--el-menu-base-level-padding) + var(--el-menu-level) * var(--el-menu-level-padding));
      i {
        color: #ffffff;
      }
      font-size: 16px;
    }

    .el-sub-menu__title {
      font-size: 16px !important;
      &:hover{
        background-color: rgba(255, 255, 255, 0.24) !important;
      }
    }

    .nest-menu .el-sub-menu__title {
      font-size: 14px !important;
    }
    .nest-menu .el-menu-item{
      .menu-title{
       font-size: 14px !important;
      }
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        // background-color: #045CFF !important;
        background-color: rgba(255, 255, 255, 0.24) !important;
      }
    }

    & .theme-dark .is-active > .el-submenu__title {
      color: $base-menu-color-active !important;
    }

    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $base-sidebar-width !important;
      font-size: 14px;
      &:hover {
        // background-color: #045CFF !important;
        background-color: rgba(255, 255, 255, 0.24) !important;
      }
    }

    .el-menu-item.is-active {
      font-weight: bold !important;
      // background-color: #045CFF !important;
      background-color: rgba(255, 255, 255, 0.24) !important;
      position: relative;
      color: #fff !important;
      i{
        font-weight: normal !important;
      }
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        width: 4px !important;
        height: 56px !important;
        background: #ffffff !important;
      }
    }

    & .theme-dark .nest-menu .el-submenu > .el-submenu__title,
    & .theme-dark .el-submenu .el-menu-item {
      padding-left: 60px !important;
      background-color: $base-sub-menu-background !important;
      &:hover {
        background-color: $base-sub-menu-hover !important;
      }
    }
    & .theme-dark .el-submenu .el-menu-item.is-active {
      font-weight: bold !important;
      background-color: #045CFF !important;
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $base-sidebar-width !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: $base-sidebar-width !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$base-sidebar-width, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      // background-color: #045CFF !important;
      background-color: rgba(255, 255, 255, 0.24) !important;
    }
  }
  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
