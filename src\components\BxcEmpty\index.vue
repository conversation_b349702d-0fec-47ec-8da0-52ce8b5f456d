<template>
  <div :style="{width: props.width,height: props.height}" class="empty-wrap">
    <img :src="props.img" alt="">
    <div class="empty-text">
      {{ props.content }}
    </div>
  </div>
</template>
<script setup>
import defaultImg from '@/assets/images/layout/empty-data.png'

const props = defineProps({
  img: {
    type: String,
    default: defaultImg
  },
  content: {
    type: String,
    default: '暂无数据...'
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  imgWidth:{
    type: String,
    default: '322px'
  }
})

</script>
<style lang="scss" scoped>
.empty-wrap {
  --imgWidth: v-bind(props.imgWidth);

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  overflow: hidden;
  img {
    margin-top: 20px;
    width: var(--imgWidth);
  }
  .empty-text {
    font-size: 14px;
    color: #888888;
    line-height: 20px;
    margin-bottom: 30px;
  }

}
</style>