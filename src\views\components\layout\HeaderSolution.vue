<template>
  <el-popover
    v-model="visible"
    popper-class="solution-wrap"
    placement="bottom-start"
    trigger="hover"
    :offset="21"
    :show-arrow="false"
    ref="solutionRef"
  >
    <div class="solution-container scroller-bar-style">
      <div class="solution-list">
        <dl v-for="(item,index) in dataList" :key="index">
          <dt>{{item.name}}</dt>
          <dd v-for="(row, i) in item.children" :key="i">
            <a class="row-title" @click.native.prevent="handleLink(row)">{{row.name}}</a>
          </dd>
        </dl>
      </div>
    </div>
    <template #reference>
      <div class="flex mr50">
        <div class="pointer">解决方案</div>
        <el-icon class="ml5"><ArrowDown /></el-icon>
      </div>
    </template>
  </el-popover>
  
</template>
<script setup>
import useUserStore from '@/store/modules/user'

const { proxy } = getCurrentInstance()
const userStore = useUserStore()
const solutionRef = ref()
const visible = ref(false)

const dataList = [
  {
    name: '企业应用解决方案',
    children: [{
      name: '标准数字化管理系统',
      path: '/solution/esms',
    }]
  },
  {
    name: '院校应用解决方案',
    children: [{
      name: '院校标准化工程综合实训系统',
      path: '',
    }]
  },
  {
    name: '行业应用解决方案',
    children: [{
      name: '智能制造试验验证公共服务平台',
      path: '',
    },{
      name: '行业标准体系智慧平台',
      path: '',
    },{
      name: '团标协作平台',
      path: '',
    }]
  },
  {
    name: '其他应用解决方案',
    children: [{
      name: '标准大数据与数字化加工系统',
      path: '',
    },{
      name: '标准信息共享服务平台',
      path: '',
    }]
  },
]

const handleLink = (row) => {
  if(!row.path) {
    proxy.$modal.msgWarning('正在建设中，敬请期待')
  }else{
    location.href = row.path
  }
  solutionRef.value.hide()
}
</script>
<style lang="scss">
.el-popover.el-popper.solution-wrap {
  padding: 0px !important;
  margin: 0px !important;
  width: 100% !important;
  height: 360px !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
  // transform:translate(0px, 60px) !important;
  inset: 0px 0px auto -5px!important; 

  .solution-container {
    height: 360px;
    margin: 0 auto;
    width: 1200px;
    overflow-y: auto;
    .solution-list {
      margin-top: 20px;
      display: flex;
      dl{
        flex: 0 0 25%;
        dt{
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }
        dd{
          padding: 0;
          margin: 15px 0 0 0;
          font-size: 14px;
          color: #888888;
        }
        .row-title{
          color: #888888;
          &:hover{
            color: $primary-color;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>