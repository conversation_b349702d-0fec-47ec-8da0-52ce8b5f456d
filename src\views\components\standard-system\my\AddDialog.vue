<template>
  <el-dialog
    :append-to-body="true"
    v-model="props.visible"
    width="600"
    title="体系设置"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent>
      <el-form-item label="体系名称" prop="systemName">
        <el-input v-model="form.systemName" placeholder="请输入体系名称" maxlength="30" />
      </el-form-item>
      <el-form-item label="标识" prop="coverUrlFileList">
        <div>
          <ele-upload-image
            :responseFn="handleResponse"
            :multiple="false"
            :fileSize="5"
            :fileType="fileType"
            v-model:value="form.coverUrlFileList"
          ></ele-upload-image>
          <span class="m-red lh20 mt10">支持格式：jpeg、jpg、png；单个文件大小不超过5MB；</span>
        </div>
      </el-form-item>
      <el-form-item label="体系描述" prop="systemDescription">
        <el-input
          v-model="form.systemDescription"
          type="textarea"
          :rows="5"
          placeholder="请输入体系描述说明信息"
          maxlength="300"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" plain>关闭</el-button>
        <el-button @click="handleSubmit" type="primary" :loading="loading">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { addSystem, getSystemDetail, updateSystem } from '@/api/standard-system/my';
  import EleUploadImage from '@/components/EleUploadImage';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: Boolean,
    id: {
      required: false,
    },
  });

  const loading = ref(false);
  const form = ref({
    coverUrlFileList: [],
  });
  const rules = reactive({
    systemName: [{ required: true, message: '请输入体系名称', trigger: 'blur' }],
  });
  const fileType = reactive(['jpeg', 'jpg', 'png']);

  const getDetail = () => {
    getSystemDetail(props.id).then(res => {
      form.value = res.data;
    });
  };

  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleSubmit = () => {
    loading.value = true;
    proxy.$refs['formRef'].validate(valid => {
      if (valid) {
        if (props.id) {
          updateSystem(form.value)
            .then(() => {
              proxy.$modal.msgSuccess('体系设置成功！');
              emit('updateData');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        } else {
          addSystem(form.value)
            .then(() => {
              proxy.$modal.msgSuccess('体系设置成功！');
              emit('updateData');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        }
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:id', '');
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'update:id', 'updateData']);

  if (props.id) getDetail();
</script>

<style lang="scss" scoped></style>
