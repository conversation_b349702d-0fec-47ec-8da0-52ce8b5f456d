<template>
  <div class="app-container bgc-ff">
    <div class="app-container-content">
      <div class="flex flex-sb">
        <div class="overview-wrap">
          <div>数据报错：<span class="num">{{ statInfo.total || '0' }}</span></div>
          <div>待处理：<span class="num">{{ statInfo.pending || '0' }}</span></div>
          <div>处理中：<span class="num">{{ statInfo.processing || '0' }}</span></div>
          <div>已处理：<span class="num">{{ statInfo.processed || '0' }}</span></div>
          <div>已撤回：<span class="num">{{ statInfo.withdraw || '0' }}</span></div>
        </div>
      </div>
      <el-table v-loading="loading" :data="dataList" :border="false" class="mt15">
        <template v-slot:empty>
          <bxc-empty class="mt30" />
        </template>
        <el-table-column label="序号" fixed width="90">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="dataTypeName" label="数据类别" width="180" show-overflow-tooltip />
        <el-table-column prop="errorTypeName" label="错误类型" width="180" show-overflow-tooltip />
        <el-table-column prop="errorDes" label="错误说明" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="提交日期" width="180" show-overflow-tooltip />
        <el-table-column prop="processStatusName" label="处理进度" width="150" show-overflow-tooltip >
          <template #default="{ row }">
            <span :class="getProcessStatusClass(row.processStatus)">
              {{ row.processStatusName }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="{ row }">
            <el-button @click="handleDetail(row)" type="primary" link>
              详情
            </el-button>
            <el-button v-if="row.processStatus == '0'" @click="handleRevoke(row)" type="primary" link>
              撤回
            </el-button>
            <el-button v-if="row.processStatus == '3'" @click="handleDelete(row)" type="primary" link>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination 
        v-show="total > 0" 
        :total="total" 
        v-model:page="queryParams.pageNum" 
        v-model:limit="queryParams.pageSize" 
        @pagination="getList" />
    </div>

    <report-errors-drawer v-if="openDetail" v-model:open="openDetail" :id="currentItem.id" />
  </div>
</template>
<script setup>
import { getErrorsList, revokeErrors, deleteErrors } from '@/api/demand/reportErrors'
import ReportErrorsDrawer from '@/views/components/demand/ReportErrorsDrawer'
import { getProcessStatusClass } from '@/utils';

const { proxy } = getCurrentInstance();

const loading = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const dataList = ref([]);
const total = ref(0);
const statInfo = ref({});
const open  = ref(false);
const openDetail  = ref(false);
const currentItem = ref({});

const getList = () => {
  loading.value = true;
  getErrorsList(queryParams.value).then(res => {
    dataList.value = res.rows || [];
    total.value = res.total || 0;
    statInfo.value = res.bean || {};
  }).finally(() => {
    loading.value = false;
  })
}

const handleDelete  =  (row) => {
  let tip = '确认删除当前报错信息？'
  proxy.$confirm(tip, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deleteErrors({ids: [row.id]}).then(res=>{
      proxy.$modal.msgSuccess('删除成功')
      getList()
    })
  }).catch(() => { })
}
const handleRevoke  =  (row) => {
  let tip = '确认撤回当前报错信息？'
  proxy.$confirm(tip, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    revokeErrors(row.id).then(res=>{
      proxy.$modal.msgSuccess('撤回成功')
      getList()
    })
  }).catch(() => { })
}
const handleDetail = (row) => {
  currentItem.value = row;
  openDetail.value = true;
}

getList()
</script>