<template>
  <el-drawer title="通知设置" size="480" append-to-body v-model="props.open" :close-on-click-modal="false" :before-close="handleClose">
    <el-collapse v-model="activeName" accordion class="app-dialog">
      <el-collapse-item title="托管通知设置" name="1">
        <div class="notice-title">通知方式设置</div>
        <div class="flex flex-sb mt20 pl20">
          <div class="c-33 f-16 f-bold">短信通知</div>
          <el-switch
            v-model="form.smsRemindType"
            inline-prompt
            active-value="1"
            inactive-value="0"
            active-text="是"
            inactive-text="否"
            @change="changeSmsRemind"
          />
        </div>
        <div class="c-99 f-14 mt5 pl20">开启短信提醒，当托管标准状态发生变化时，系统将第一时间通过短信方式告诉您托管标准的状态变化信息</div>

        <!-- <div class="flex flex-sb mt20 pl20">
          <div class="c-33 f-16 f-bold">邮件通知</div>
          <el-switch
            v-model="form.emailRemindType"
            inline-prompt
            active-value="1"
            inactive-value="0"
            active-text="是"
            inactive-text="否"
            @change="changeEmailRemind"
          />
        </div>
        <div class="c-99 f-14 mt5 pl20">开启邮件提醒，当托管标准状态发生变化时，系统将第一时间通过短信方式告诉您托管标准的状态变化信息</div> -->

        <div class="flex flex-sb mt20 pl20">
          <div class="c-33 f-16 f-bold">站内通知</div>
          <el-switch
            v-model="form.msgRemindType"
            inline-prompt
            active-value="1"
            inactive-value="0"
            active-text="是"
            inactive-text="否"
            @change="changeMsgRemind"
          />
        </div>
        <div class="c-99 f-14 mt5 pl20">系统将通过站内信息方式告诉您托管标准的状态变化信息</div>
      
        <div class="notice-title mt50">通知时间设置</div>
        <div class="flex flex-sb mt20 pl20">
          <div class="c-33 f-16 f-bold flex-1">通知时间</div>
          <el-select
            v-model="form.remindTime"
            placeholder="请选择通知时间"
            class="flex-1"
            @change="changeRemindTime"
          >
            <el-option
              v-for="item in bxc_remind_time"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="c-99 f-14 mt5 pl20">设置通知时间，当标准状态发生变化时，平台将在设置的通知时间向指定通知方式发送通知信息</div>

      </el-collapse-item>
    </el-collapse>
  </el-drawer>
</template>

<script setup>
import { setSmsRemind, setEmailRemind, setMsgRemind, setRemindTime } from '@/api/customer'
import useUserStore from '@/store/modules/user'

const { proxy } = getCurrentInstance();
const { bxc_remind_time } = proxy.useDict('bxc_remind_time')
const userStore = useUserStore()

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
});

const loading = ref(false);
const activeName = ref('1')
const form = ref({
  smsRemindType: '0',
  emailRemindType: '0',
  msgRemindType: '0',
  remindTime: undefined
});


const emit = defineEmits(['update:open','success']);

const initData = () => {
  form.value.smsRemindType = userStore.userInfo.smsRemindType
  form.value.emailRemindType = userStore.userInfo.emailRemindType
  form.value.msgRemindType = userStore.userInfo.msgRemindType
  form.value.remindTime = userStore.userInfo.remindTime
};
const changeSmsRemind = (val) => {
  setSmsRemind({onOff: val}).then((res) => {
    refreshUserInfo()
  }).finally(() => {
    
  });
}
const changeEmailRemind = (val) => {
  setEmailRemind({onOff: val}).then((res) => {
    refreshUserInfo()
  }).finally(() => {
    
  });
}
const changeMsgRemind = (val) => {
  setMsgRemind({onOff: val}).then((res) => {
    refreshUserInfo()
  }).finally(() => {
    
  });
}
const changeRemindTime = (val) => {
  setRemindTime({remindTime: val}).then((res) => {
    refreshUserInfo()
  }).finally(() => {
    
  });
}

const refreshUserInfo = () => {
  userStore.getInfo()
}
const handleClose = () => {
  emit('update:open', false);
};

initData()
</script>
