import { createWebHistory, createRouter } from 'vue-router';
/* Layout */
import Layout from '@/layout';

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '工作台', icon: 'shiliangzhinengduixiang18-01', affix: true },
      },
    ],
  },
  {
    path: '/trusteeship',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/trusteeship/index'),
        name: 'TrusteeshipIndex',
        meta: { title: '标准托管', icon: 'tuoguan', affix: true },
      },
    ],
  },
  {
    path: '/standard-system',
    component: Layout,
    meta: { title: '标准体系', icon: 'tubiao-25', affix: true },
    children: [
      {
        path: 'subscription',
        component: () => import('@/views/standard-system/subscription/index'),
        name: 'SubscriptionIndex',
        meta: { title: '订阅体系', icon: '', affix: true },
      },
      {
        path: 'my',
        component: () => import('@/views/standard-system/my/index'),
        name: 'MyIndex',
        meta: { title: '我的体系', icon: '', affix: true },
      },
      {
        path: 'manage',
        component: () => import('@/views/standard-system/my/manage'),
        name: 'MyManage',
        hidden: true,
        meta: { title: '体系管理', activeMenu: '/standard-system/my' },
      },
      {
        path: 'detail',
        component: () => import('@/views/standard-system/my/detail'),
        name: 'MyDetail',
        hidden: true,
        meta: { title: '体系详情', activeMenu: '/standard-system/my' },
      },
    ],
  },
  {
    path: '/collect',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/collect/index'),
        name: 'CollectIndex',
        meta: { title: '我的收藏', icon: 'shoucang21', affix: true },
      },
    ],
  },
  {
    path: '/demand',
    component: Layout,
    meta: { title: '服务需求', icon: 'fuwu', affix: true },
    children: [
      {
        path: 'consult',
        component: () => import('@/views/demand/consult/index'),
        name: 'DemandConsultIndex',
        meta: { title: '需求咨询', icon: '', affix: true },
      },
      {
        path: 'feedback',
        component: () => import('@/views/demand/feedback/index'),
        name: 'DemandFeedbackIndex',
        meta: { title: '服务反馈', icon: '', affix: true },
      },
      {
        path: 'reportErrors',
        component: () => import('@/views/demand/reportErrors/index'),
        name: 'DemandReportErrorsIndex',
        meta: { title: '数据报错', icon: '', affix: true },
      },
    ],
  },
  {
    path: '/message',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/message/index'),
        name: 'MessageIndex',
        meta: { title: '消息通知', icon: 'zixunguanli', affix: true },
      },
    ],
  },
  {
    path: '/customer',
    component: Layout,
    meta: { title: '用户中心', icon: 'yonghuzhongxin', affix: true },
    children: [
      {
        path: 'account',
        component: () => import('@/views/customer/account/index'),
        name: 'CustomerAccountIndex',
        meta: { title: '账户信息', icon: '', affix: true },
      },
      {
        path: 'order',
        component: () => import('@/views/customer/order/index'),
        name: 'CustomerOrderIndex',
        meta: { title: '订单管理', icon: '', affix: true },
      },
      {
        path: 'history',
        component: () => import('@/views/customer/history/index'),
        name: 'CustomerHistoryIndex',
        meta: { title: '浏览历史', icon: '', affix: true },
      },
    ],
  },
  // {
  //   path: '/user',
  //   component: Layout,
  //   hidden: true,
  //   redirect: 'noredirect',
  //   children: [
  //     {
  //       path: 'profile',
  //       component: () => import('@/views/system/user/profile/index'),
  //       name: 'Profile',
  //       meta: { title: '个人中心', icon: 'user' }
  //     }
  //   ]
  // }
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/standard-stat',
    component: Layout,
    meta: { title: '标准分析', icon: 'shujufenxi', affix: true, auth: ['0', '1'] },
    alwaysShow: true,
    children: [
      {
        path: 'data-stat',
        component: () => import('@/views/standard-stat/data-stat/index'),
        name: 'StandardStatDataStatIndex',
        meta: { title: '数据分析', icon: '', affix: true, auth: ['0'] },
      },
      {
        path: 'data-stat/gb',
        component: () => import('@/views/standard-stat/data-stat/gb'),
        name: 'StandardStatDataStatGB',
        meta: { title: '国家标准统计', activeMenu: '/standard-stat/data-stat', auth: ['0'] },
        hidden: true,
      },
      {
        path: 'data-stat/hb',
        component: () => import('@/views/standard-stat/data-stat/hb'),
        name: 'StandardStatDataStatHB',
        meta: { title: '行业标准统计', activeMenu: '/standard-stat/data-stat', auth: ['0'] },
        hidden: true,
      },
      {
        path: 'data-stat/db',
        component: () => import('@/views/standard-stat/data-stat/db'),
        name: 'StandardStatDataStatDB',
        meta: { title: '地方标准统计', activeMenu: '/standard-stat/data-stat', auth: ['0'] },
        hidden: true,
      },
      {
        path: 'data-stat/tb',
        component: () => import('@/views/standard-stat/data-stat/tb'),
        name: 'StandardStatDataStatTB',
        meta: { title: '团体标准统计', activeMenu: '/standard-stat/data-stat', auth: ['0'] },
        hidden: true,
      },
      {
        path: 'data-stat/qb',
        component: () => import('@/views/standard-stat/data-stat/qb'),
        name: 'StandardStatDataStatQB',
        meta: { title: '企业标准统计', activeMenu: '/standard-stat/data-stat', auth: ['0'] },
        hidden: true,
      },
      {
        path: 'fill-stat',
        component: () => import('@/views/standard-stat/fill-stat/index'),
        name: 'StandardStatFillStatIndex',
        meta: { title: '填报分析', icon: '', affix: true, auth: ['1'] },
      },
      {
        path: 'fill-stat/detail',
        component: () => import('@/views/standard-stat/fill-stat/detail'),
        name: 'StandardStatFillStatDetail',
        meta: { title: '填报分析详情', activeMenu: '/standard-stat/fill-stat', auth: ['1'] },
        hidden: true,
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory('/user-center/'),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

export default router;
