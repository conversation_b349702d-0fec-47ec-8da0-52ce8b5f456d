<template>
  <el-dialog
    width="500px"
    title="导入结果"
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="flex flex-center mt10">
      <span class="iconfont icon-dui f-20 m-green mr10"></span>
      <span class="f-18 c-33 f-bold">批量导入成功</span>
    </div>
    <div class="mt20 mb10">
      <div class="flex flex-sb f-14">
        <div>
          标准总数：
          <span class="c-primary">{{ rowData.total || 0 }}</span>
          条
        </div>
        |
        <div>
          导入成功：
          <span class="c-primary">{{ rowData.success || 0 }}</span>
          条
        </div>
        |
        <div>
          导入失败：
          <span class="c-primary">{{ rowData.fail || 0 }}</span>
          条
        </div>
      </div>
      <div
        v-if="rowData.fail != 0"
        @click="handleDownload"
        class="c-primary mt20 f-14 flex flex-center pointer"
        style="text-decoration: underline"
      >
        下载导入失败数据
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
  import { json2excel } from '@/utils/formatExcel.js';
  const props = defineProps({
    rowData: {
      required: true,
      type: Object,
      default: () => {
        return {};
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const { rowData } = toRefs(props);

  const handleDownload = () => {
    let data = [];
    if (rowData.value.msg && rowData.value.msg.length > 0) {
      rowData.value.msg.forEach(item => {
        data.push({ standardCode: item });
      });
    }
    let excelDatas = [
      {
        tHeader: ['标准号'],
        filterVal: ['standardCode'],
        tableDatas: data,
        sheetName: 'Sheet1',
      },
    ];
    let multiHeader = ['导入失败数据'];
    json2excel(excelDatas, multiHeader, '导入失败数据', true, 'xlsx');
  };

  const handleClose = () => {
    emit('updateData');
    emit('update:rowData', {});
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'update:rowData', 'updateData']);
</script>

<style lang="scss" scoped></style>
