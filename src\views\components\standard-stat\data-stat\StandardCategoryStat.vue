<template>
  <div class="standard-chat">
    <div class="chat-title">标准类别统计</div>
    <div class="chat-search">
      <chat-search-form v-model:queryParams="queryParams" @change="getData" />
    </div>
    <div class="chat-content">
      <stat-pie-chart v-if="chartDataPie && chartDataPie.length > 0" :chart-data="chartDataPie" :height="'380px'" />
        <bxc-empty v-else height="380px" />
    </div>
  </div>
</template>
<script setup>
import ChatSearchForm from '@/views/components/standard-stat/data-stat/ChatSearchForm'
import StatPieChart from '@/views/components/standard-stat/data-stat/StatPieChart'
import { getStandardCategoryStat } from '@/api/standard-stat'

const props = defineProps(['standardType'])

const queryParams = ref({
  publishStartDate: undefined,
  publishEndDate: undefined,
  standardType: props.standardType, // 0: 国家标准, 1: 行业标准, 2: 地方标准, 3: 团体标准, 4: 企业标准, 8: 规范性文件, 9: 计量技术规范
  standardStatusList: ['1']
})
const chartDataPie = ref([])
const loading = ref(false)

const getData = () => {
  loading.value = true;
  getStandardCategoryStat(queryParams.value).then((response) => {
    if(response.data){
      chartDataPie.value = response.data
    }
    loading.value = false
  }).catch(() => {
    loading.value = false
  });
}

getData()
</script>