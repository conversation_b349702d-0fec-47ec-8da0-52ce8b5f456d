<template>
  <div>
    <el-table v-loading="loading" :data="tableData" :border="false">
      <template v-slot:empty>
        <bxc-empty class="mt30" />
      </template>
      <el-table-column label="序号" fixed width="90">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="知识名称" min-width="200" fixed="left" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('jump', '/classroom/knowledge/detail?id=' + row.recordId)" class="c-primary pointer">
            {{ row.knowledgeName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="noticeCode" label="主图" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <img v-if="row.mainImageList && row.mainImageList.length > 0" :src="row.mainImageList[0].url" class="_img" alt="" />
        </template>
      </el-table-column>
      <el-table-column prop="economicTypeName" label="所属产业" min-width="180" show-overflow-tooltip />
      <el-table-column prop="standardCodeName" label="关联标准" min-width="180" show-overflow-tooltip />
      <el-table-column prop="createTime" label="收藏时间" min-width="180" show-overflow-tooltip />
      <el-table-column label="操作" min-width="100" fixed="right" show-overflow-tooltip>
        <template #default="{ row }">
          <span @click="handleClick('cancel', row)" class="c-primary pointer">取消收藏</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
  import { upadteCollect } from '@/api/collect';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    bean: {
      type: Object,
      default: () => {
        return {};
      },
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    total: {
      type: [String, Number],
      default: 0,
    },
    queryParams: {
      type: Object,
    },
  });

  const { loading, bean, tableData, queryParams } = toRefs(props);

  const handleClick = (type, data) => {
    switch (type) {
      case 'jump':
        window.open(data, '_blank');
        break;
      case 'cancel':
        proxy
          .$confirm('确认取消收藏当前数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            upadteCollect({ id: data.id, recordId: data.recordId, recordType: 8, isCollect: 0 }).then(res => {
              proxy.$modal.msgSuccess('取消收藏成功！');
              emit('updateData');
            });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  const emit = defineEmits(['updateData']);
</script>

<style lang="scss" scoped>
._img {
  width: 150px;
  max-height: 100px;
}
</style>
