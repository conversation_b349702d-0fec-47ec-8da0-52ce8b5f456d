import request from '@/utils/request';

// 查询未读消息消息top和未读消息数
export function getMessageList() {
  return request({
    url: '/business/messagePublish/unReadCountTopAndCount',
    method: 'get',
  });
}
// 标记已读/全部标记
export function setMessageRead(messageId) {
  let url = messageId ? '/business/messagePublish/readTag?messageId=' + messageId : '/business/messagePublish/readTag';
  return request({
    url: url,
    method: 'put',
  });
}

// 消息列表
export function getMessageAllList(params) {
  return request({
    url: '/business/messagePublish/list',
    method: 'get',
    params,
  });
}

// 删除
export function deleteMessage(data) {
  return request({
    url: '/business/messagePublish/remove',
    method: 'delete',
    data,
  });
}

// 详情
export function getMessageDetail(data) {
  return request({
    url: '/business/messagePublish/' + data,
    method: 'get',
  });
}

// 已读
export function putMessageStatus(data) {
  return request({
    url: '/business/messagePublish/readTag',
    method: 'put',
    data,
  });
}
